# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/@alloc/quick-lru/download/@alloc/quick-lru-5.2.0.tgz"
  integrity sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=

"@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2":
  version "7.28.4"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.28.4.tgz"
  integrity sha1-lv3xrxuIWchHSrOcKVMSv7fCSwQ=

"@babel/core@^7.28.0":
  version "7.28.4"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.28.4.tgz"
  integrity sha1-EqVQuHlEUt9MiwhPlQA7zhdC1JY=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.28.3"
    "@babel/helpers" "^7.28.4"
    "@babel/parser" "^7.28.4"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.4"
    "@babel/types" "^7.28.4"
    "@jridgewell/remapping" "^2.3.5"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.28.3.tgz"
  integrity sha1-libBdBxlDLrDkSFpSg8tdFG47z4=
  dependencies:
    "@babel/parser" "^7.28.3"
    "@babel/types" "^7.28.2"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    jsesc "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-globals@^7.28.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-globals/download/@babel/helper-globals-7.28.0.tgz"
  integrity sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.28.3.tgz"
  integrity sha1-orN9PaOyNE/ghdqyNEJvK5ovpfY=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helpers@^7.28.4":
  version "7.28.4"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.28.4.tgz"
  integrity sha1-/gcnR0LpW9988UQ1k+64kmq2OCc=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.27.2", "@babel/parser@^7.28.3", "@babel/parser@^7.28.4":
  version "7.28.4"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.28.4.tgz"
  integrity sha1-2iXUZDUyiQkyzAP3cF/hljfgP6g=
  dependencies:
    "@babel/types" "^7.28.4"

"@babel/plugin-transform-react-jsx-self@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.27.1.tgz"
  integrity sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.25.9.tgz"
  integrity sha1-TGuNqlILXxVbX7VVR9fJ+pFBdQM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx-source@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.27.1.tgz"
  integrity sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/runtime@^7.2.0", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.7":
  version "7.28.4"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.28.4.tgz"
  integrity sha1-pwImAW+r4lxXg7LyLT4cm8XKMyY=

"@babel/template@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.28.3", "@babel/traverse@^7.28.4":
  version "7.28.4"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.28.4.tgz"
  integrity sha1-jUVhAblqsXXUhySfYGgCIWkrlYs=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.4"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"
    debug "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.28.2", "@babel/types@^7.28.4":
  version "7.28.4"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.28.4.tgz"
  integrity sha1-Ck5hj0xgp81sEcstSAYOTb44rDo=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@colors/colors@1.6.0", "@colors/colors@^1.6.0":
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/@colors/colors/download/@colors/colors-1.6.0.tgz"
  integrity sha1-7GzSN0QHALwjyiMIf1E8dVCJWLA=

"@dabh/diagnostics@^2.0.2":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@dabh/diagnostics/download/@dabh/diagnostics-2.0.3.tgz"
  integrity sha1-f36X7ppyXf/HgI2TZozJhOHcR3o=
  dependencies:
    colorspace "1.1.x"
    enabled "2.0.x"
    kuler "^2.0.0"

"@esbuild/aix-ppc64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f"
  integrity sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=

"@esbuild/android-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052"
  integrity sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=

"@esbuild/android-arm@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28"
  integrity sha1-mwQ4T7dxkm36bXrQQyTssqubLig=

"@esbuild/android-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e"
  integrity sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=

"@esbuild/darwin-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.21.5.tgz"
  integrity sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=

"@esbuild/darwin-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22"
  integrity sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=

"@esbuild/freebsd-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e"
  integrity sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=

"@esbuild/freebsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261"
  integrity sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=

"@esbuild/linux-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b"
  integrity sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=

"@esbuild/linux-arm@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9"
  integrity sha1-/G/RGorKVsH284lPK+oEefj2Jrk=

"@esbuild/linux-ia32@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2"
  integrity sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=

"@esbuild/linux-loong64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df"
  integrity sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=

"@esbuild/linux-mips64el@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe"
  integrity sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=

"@esbuild/linux-ppc64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4"
  integrity sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=

"@esbuild/linux-riscv64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc"
  integrity sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=

"@esbuild/linux-s390x@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de"
  integrity sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0"
  integrity sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=

"@esbuild/netbsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047"
  integrity sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=

"@esbuild/openbsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70"
  integrity sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=

"@esbuild/sunos-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b"
  integrity sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=

"@esbuild/win32-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d"
  integrity sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=

"@esbuild/win32-ia32@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b"
  integrity sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.21.5.tgz#acad351d582d157bb145535db2a6ff53dd514b5c"
  integrity sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.9.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.9.0.tgz"
  integrity sha1-cwjfFY4GTw3YuP21iqFPoqf5E7M=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-8.57.1.tgz"
  integrity sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=

"@faker-js/faker@^10.0.0":
  version "10.0.0"
  resolved "http://r.npm.sankuai.com/@faker-js/faker/download/@faker-js/faker-10.0.0.tgz"
  integrity sha1-3xHwuistckNXTjQ2SUjWolFjxvQ=

"@floating-ui/core@^1.7.3":
  version "1.7.3"
  resolved "http://r.npm.sankuai.com/@floating-ui/core/download/@floating-ui/core-1.7.3.tgz"
  integrity sha1-Ri1yLwAeI+Rthv0r0NIbdpPMuLc=
  dependencies:
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/dom@^1.7.4":
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/@floating-ui/dom/download/@floating-ui/dom-1.7.4.tgz"
  integrity sha1-7mZ1SZmHRcnD4+hGg7kJwx1smnc=
  dependencies:
    "@floating-ui/core" "^1.7.3"
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/react-dom@^2.0.0":
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/@floating-ui/react-dom/download/@floating-ui/react-dom-2.1.6.tgz"
  integrity sha1-GJ9oEEPBQAVh9ily9GG5PwG/IjE=
  dependencies:
    "@floating-ui/dom" "^1.7.4"

"@floating-ui/utils@^0.2.10":
  version "0.2.10"
  resolved "http://r.npm.sankuai.com/@floating-ui/utils/download/@floating-ui/utils-0.2.10.tgz"
  integrity sha1-oqHjgS0UUl9yXQEac+zrQf71vBw=

"@hookform/resolvers@^3.6.0":
  version "3.10.0"
  resolved "http://r.npm.sankuai.com/@hookform/resolvers/download/@hookform/resolvers-3.10.0.tgz"
  integrity sha1-e/0YET2spOV+J+EgW31aLTcapZo=

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz"
  integrity sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.13"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.13.tgz"
  integrity sha1-Y0Khn0Q0dRjJPkOxrGnes8Rlah8=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/remapping@^2.3.5":
  version "2.3.5"
  resolved "http://r.npm.sankuai.com/@jridgewell/remapping/download/@jridgewell/remapping-2.3.5.tgz"
  integrity sha1-N1xHbRlylHhRuh4Vro8SMEdEWqE=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.5"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.5.tgz"
  integrity sha1-aRKwDSxjHA0Vzhp6tXzWV/Ko+Lo=

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.28":
  version "0.3.31"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.31.tgz"
  integrity sha1-2xXWeByTHzolGj2sOVAcmKYIL9A=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@meituan-nocode/vite-plugin-dev-logger@^0.1.0":
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/@meituan-nocode/vite-plugin-dev-logger/download/@meituan-nocode/vite-plugin-dev-logger-0.1.0.tgz"
  integrity sha1-Kdu+F+Ai8KhW3Ykl80p7sI1sXbA=
  dependencies:
    winston "^3.17.0"
    winston-daily-rotate-file "^5.0.0"

"@meituan-nocode/vite-plugin-nocode-html-transformer@^0.3.1":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@meituan-nocode/vite-plugin-nocode-html-transformer/download/@meituan-nocode/vite-plugin-nocode-html-transformer-0.3.1.tgz"
  integrity sha1-D6ipnOVE4xzD4EhpSF+v5+X7Mvo=

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "http://r.npm.sankuai.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@radix-ui/number@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/number/download/@radix-ui/number-1.1.1.tgz"
  integrity sha1-eyySJfvxsSZTlVH1mFdp0ASNkJA=

"@radix-ui/primitive@1.1.3":
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/@radix-ui/primitive/download/@radix-ui/primitive-1.1.3.tgz"
  integrity sha1-4tvBO9xeQWj0M091gy173T4t5bo=

"@radix-ui/react-accordion@^1.2.0":
  version "1.2.12"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-accordion/download/@radix-ui/react-accordion-1.2.12.tgz"
  integrity sha1-H9cNTvNgGAErngMyT/GG3nopwT8=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collapsible" "1.1.12"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-alert-dialog@^1.1.0":
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-alert-dialog/download/@radix-ui/react-alert-dialog-1.1.15.tgz"
  integrity sha1-+nUdD92aoqkJYcmQHboY5jjdS0E=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dialog" "1.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-arrow@1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-arrow/download/@radix-ui/react-arrow-1.1.7.tgz"
  integrity sha1-4UomV8gdlhWYxecrc91gmKzATwk=
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-aspect-ratio@^1.1.0":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-aspect-ratio/download/@radix-ui/react-aspect-ratio-1.1.7.tgz"
  integrity sha1-ldCtzd3Q1Axd0q4HyGCLTwuYP1M=
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-avatar@^1.1.0":
  version "1.1.10"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-avatar/download/@radix-ui/react-avatar-1.1.10.tgz"
  integrity sha1-xYqIAO89PueDsxaP7nx29lNL/ZM=
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-is-hydrated" "0.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-checkbox@^1.1.0":
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-checkbox/download/@radix-ui/react-checkbox-1.3.3.tgz"
  integrity sha1-20XKim1cBWqS907btWSs7gUxi3k=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-collapsible@1.1.12", "@radix-ui/react-collapsible@^1.1.0":
  version "1.1.12"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-collapsible/download/@radix-ui/react-collapsible-1.1.12.tgz"
  integrity sha1-4sxppEkKKSD5fDwxULC/ISgePEk=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-collection@1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-collection/download/@radix-ui/react-collection-1.1.7.tgz"
  integrity sha1-0FwlyprEaVzBm6kfQvaG4+otmuw=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-compose-refs@1.1.2", "@radix-ui/react-compose-refs@^1.1.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-compose-refs/download/@radix-ui/react-compose-refs-1.1.2.tgz"
  integrity sha1-osTEevYzcEjueP9twNCQs5DSuzA=

"@radix-ui/react-context-menu@^2.2.0":
  version "2.2.16"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-context-menu/download/@radix-ui/react-context-menu-2.2.16.tgz"
  integrity sha1-57+UpFe2ivCPJK1paUkURTD6q1A=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-menu" "2.1.16"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-context@1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-context/download/@radix-ui/react-context-1.1.2.tgz"
  integrity sha1-YWKO8mmkMzgsNk9vHjeIptwhOjY=

"@radix-ui/react-dialog@1.1.15", "@radix-ui/react-dialog@^1.1.0", "@radix-ui/react-dialog@^1.1.1", "@radix-ui/react-dialog@^1.1.6":
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-dialog/download/@radix-ui/react-dialog-1.1.15.tgz"
  integrity sha1-HePXp+mheph00pwH9ZQKGKEZtjI=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-focus-guards" "1.1.3"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-direction@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-direction/download/@radix-ui/react-direction-1.1.1.tgz"
  integrity sha1-OeWldp5nbHUyBLeS++bPUI5VChQ=

"@radix-ui/react-dismissable-layer@1.1.11":
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-dismissable-layer/download/@radix-ui/react-dismissable-layer-1.1.11.tgz"
  integrity sha1-4zq29r2qAPj3MnxAjZ9jE3a4izc=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dropdown-menu@^2.1.0":
  version "2.1.16"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-dropdown-menu/download/@radix-ui/react-dropdown-menu-2.1.16.tgz"
  integrity sha1-XuBFxiutgSI0eYHEedkrH/JMclQ=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.16"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-focus-guards@1.1.3":
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-focus-guards/download/@radix-ui/react-focus-guards-1.1.3.tgz"
  integrity sha1-KlZp5GStX96fhtIvf9wXeBpN+n8=

"@radix-ui/react-focus-scope@1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-focus-scope/download/@radix-ui/react-focus-scope-1.1.7.tgz"
  integrity sha1-3+dvwQNTfYC/QnI6GDdz/Qe/tY0=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-hover-card@^1.1.0":
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-hover-card/download/@radix-ui/react-hover-card-1.1.15.tgz"
  integrity sha1-m8ftVcN6kDKs38x8+lxzsRfP/l4=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-popper" "1.2.8"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-id@1.1.1", "@radix-ui/react-id@^1.1.0":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-id/download/@radix-ui/react-id-1.1.1.tgz"
  integrity sha1-FAQALnmgP+Bit+OGSqAeJL0Ucfc=
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-label@^2.1.0":
  version "2.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-label/download/@radix-ui/react-label-2.1.7.tgz"
  integrity sha1-rZWf+cbklo1TMynrlWluG6itcqs=
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-menu@2.1.16":
  version "2.1.16"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-menu/download/@radix-ui/react-menu-2.1.16.tgz"
  integrity sha1-Uopalzw6dBPT1J65zNIpqlJAKRE=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-focus-guards" "1.1.3"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.8"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.11"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-menubar@^1.1.0":
  version "1.1.16"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-menubar/download/@radix-ui/react-menubar-1.1.16.tgz"
  integrity sha1-Xt9+ov96p+O6iWs1z1d/EiFgEhw=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.16"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.11"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-navigation-menu@^1.2.0":
  version "1.2.14"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-navigation-menu/download/@radix-ui/react-navigation-menu-1.2.14.tgz"
  integrity sha1-Tm0Rcr48iXUuVk+HIXBveFdK190=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-popover@^1.1.0":
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-popover/download/@radix-ui/react-popover-1.1.15.tgz"
  integrity sha1-nIUvk5kKaH69yUmyw94fN83ExdU=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-focus-guards" "1.1.3"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.8"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popper@1.2.8":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-popper/download/@radix-ui/react-popper-1.2.8.tgz"
  integrity sha1-p585zdKwmrn7UL+VJQkYQixNlgI=
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-portal@1.1.9":
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-portal/download/@radix-ui/react-portal-1.1.9.tgz"
  integrity sha1-FMNkn+SOxHSsUe2fK59dpNkcRHI=
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.5":
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-presence/download/@radix-ui/react-presence-1.1.5.tgz"
  integrity sha1-XY8orDFsMvB4r84ploOSUMEGk9s=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-primitive@2.1.3", "@radix-ui/react-primitive@^2.0.2":
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-primitive/download/@radix-ui/react-primitive-2.1.3.tgz"
  integrity sha1-25uLz/SeAb5RCteYk/sOTNpQ8bw=
  dependencies:
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-progress@^1.1.0":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-progress/download/@radix-ui/react-progress-1.1.7.tgz"
  integrity sha1-ordjmLPyS2vV438RKx4w++3U844=
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-radio-group@^1.2.0":
  version "1.3.8"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-radio-group/download/@radix-ui/react-radio-group-1.3.8.tgz"
  integrity sha1-k/ECtblI1gLC8q2xvFw0fLr2S9k=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.11"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-roving-focus@1.1.11":
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-roving-focus/download/@radix-ui/react-roving-focus-1.1.11.tgz"
  integrity sha1-71Q4S3Nhr8ZIDc+ZB+8v7bUID9k=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-scroll-area@^1.1.0":
  version "1.2.10"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-scroll-area/download/@radix-ui/react-scroll-area-1.2.10.tgz"
  integrity sha1-5P07Snm7d77BpS8MjybY8/HKSyI=
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-select@^2.1.0":
  version "2.2.6"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-select/download/@radix-ui/react-select-2.2.6.tgz"
  integrity sha1-Aiz42rFr8F0NG0355T5L6ht0T9k=
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-focus-guards" "1.1.3"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.8"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-separator@^1.1.0":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-separator/download/@radix-ui/react-separator-1.1.7.tgz"
  integrity sha1-oYvX/QfBD9obuhTyowMuexorNHA=
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-slider@^1.2.0":
  version "1.3.6"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-slider/download/@radix-ui/react-slider-1.3.6.tgz"
  integrity sha1-QJRTEQuPNMoAlydQuAzXkvCyOow=
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-slot@1.2.3", "@radix-ui/react-slot@^1.1.0":
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-slot/download/@radix-ui/react-slot-1.2.3.tgz"
  integrity sha1-UC1uNU/IR9QWnDvF8Yned39oz+E=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-switch@^1.1.0":
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-switch/download/@radix-ui/react-switch-1.2.6.tgz"
  integrity sha1-/3msuDHw1eqSFs/MW5OZElcTWOM=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-tabs@^1.1.0":
  version "1.1.13"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-tabs/download/@radix-ui/react-tabs-1.1.13.tgz"
  integrity sha1-NTfON51+f/TutrZ6CXPhOcKsHxU=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.11"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toast@^1.2.0":
  version "1.2.15"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-toast/download/@radix-ui/react-toast-1.2.15.tgz"
  integrity sha1-dGz5qBKX3b+6IU5cgSReo/cG+HY=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-toggle-group@^1.1.0":
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-toggle-group/download/@radix-ui/react-toggle-group-1.1.11.tgz"
  integrity sha1-5RPW/9sHUJtACrWybyUjdHwNUcE=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.11"
    "@radix-ui/react-toggle" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toggle@1.1.10", "@radix-ui/react-toggle@^1.1.0":
  version "1.1.10"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-toggle/download/@radix-ui/react-toggle-1.1.10.tgz"
  integrity sha1-sEug+WCVmd9mb85bLzgQmhl/CM8=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-tooltip@^1.1.0":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-tooltip/download/@radix-ui/react-tooltip-1.2.8.tgz"
  integrity sha1-P1AmfiW8z8niC7MDa/2atMLDDCw=
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.8"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-use-callback-ref@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-callback-ref/download/@radix-ui/react-use-callback-ref-1.1.1.tgz"
  integrity sha1-YqTbqLMlX9xcx3h/rqwcbkzFjUA=

"@radix-ui/react-use-controllable-state@1.2.2":
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-controllable-state/download/@radix-ui/react-use-controllable-state-1.2.2.tgz"
  integrity sha1-kFeTQF3lfWGkOfSv67sX0GRfMZA=
  dependencies:
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-effect-event@0.0.2":
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-effect-event/download/@radix-ui/react-use-effect-event-0.0.2.tgz"
  integrity sha1-CQzzDQCkx2MqFVSFEukVIhdZOQc=
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-escape-keydown@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-escape-keydown/download/@radix-ui/react-use-escape-keydown-1.1.1.tgz"
  integrity sha1-s/7Zu+o2ahGPQEJ6xAUAqhQjzCk=
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-is-hydrated@0.1.0":
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-is-hydrated/download/@radix-ui/react-use-is-hydrated-0.1.0.tgz"
  integrity sha1-VE2nM2lRcDbHdlnXzdAZ3A9f+aA=
  dependencies:
    use-sync-external-store "^1.5.0"

"@radix-ui/react-use-layout-effect@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-layout-effect/download/@radix-ui/react-use-layout-effect-1.1.1.tgz"
  integrity sha1-DEIwqe7UnUWJyWfi2cDZ1gojlx4=

"@radix-ui/react-use-previous@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-previous/download/@radix-ui/react-use-previous-1.1.1.tgz"
  integrity sha1-GhrVVolz0kBR7Qr2h3ZvbHy5tbU=

"@radix-ui/react-use-rect@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-rect/download/@radix-ui/react-use-rect-1.1.1.tgz"
  integrity sha1-AUQ8qO0HHTMCPBET5Rc7Xth2kVI=
  dependencies:
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-use-size@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-size/download/@radix-ui/react-use-size-1.1.1.tgz"
  integrity sha1-beJ2/7w4mlN//kMW9bDyQSlAWzc=
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-visually-hidden@1.2.3":
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-visually-hidden/download/@radix-ui/react-visually-hidden-1.2.3.tgz"
  integrity sha1-qMOMhgdzXcnwXDL4erD5wrEJ778=
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/rect@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/rect/download/@radix-ui/rect-1.1.1.tgz"
  integrity sha1-eCRO/hKTDFb9JV15I4ZYV8QayMs=

"@remix-run/router@1.23.0":
  version "1.23.0"
  resolved "http://r.npm.sankuai.com/@remix-run/router/download/@remix-run/router-1.23.0.tgz"
  integrity sha1-NTkNDnd5YmwCaxE3baZ4nrg4kkI=

"@rolldown/pluginutils@1.0.0-beta.27":
  version "1.0.0-beta.27"
  resolved "http://r.npm.sankuai.com/@rolldown/pluginutils/download/@rolldown/pluginutils-1.0.0-beta.27.tgz"
  integrity sha1-R9K/TO9tRwsi9YMbQg+JZOC/dV8=

"@rollup/rollup-android-arm-eabi@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.50.2.tgz#52d66eba5198155f265f54aed94d2489c49269f6"
  integrity sha1-UtZuulGYFV8mX1Su2U0kicSSafY=

"@rollup/rollup-android-arm64@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.50.2.tgz#137e8153fc9ce6757531ce300b8d2262299f758e"
  integrity sha1-E36BU/yc5nV1Mc4wC40iYimfdY4=

"@rollup/rollup-darwin-arm64@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.50.2.tgz"
  integrity sha1-1K/ZBDhtNxks9e9zRf2w3RusC8M=

"@rollup/rollup-darwin-x64@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.50.2.tgz#6dbe83431fc7cbc09a2b6ed2b9fb7a62dd66ebc2"
  integrity sha1-bb6DQx/Hy8CaK27Suft6Yt1m68I=

"@rollup/rollup-freebsd-arm64@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.50.2.tgz#d35afb9f66154b557b3387d12450920f8a954b96"
  integrity sha1-01r7n2YVS1V7M4fRJFCSD4qVS5Y=

"@rollup/rollup-freebsd-x64@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.50.2.tgz#849303ecdc171a420317ad9166a70af308348f34"
  integrity sha1-hJMD7NwXGkIDF62RZqcK8wg0jzQ=

"@rollup/rollup-linux-arm-gnueabihf@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.50.2.tgz#ab36199ca613376232794b2f3ba10e2b547a447c"
  integrity sha1-qzYZnKYTN2IyeUsvO6EOK1R6RHw=

"@rollup/rollup-linux-arm-musleabihf@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.50.2.tgz#f3704bc2eaecd176f558dc47af64197fcac36e8a"
  integrity sha1-83BLwurs0Xb1WNxHr2QZf8rDboo=

"@rollup/rollup-linux-arm64-gnu@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.50.2.tgz#dda0b06fd1daedd00b34395a2fb4aaaa2ed6c32b"
  integrity sha1-3aCwb9Ha7dALNDlaL7Sqqi7Wwys=

"@rollup/rollup-linux-arm64-musl@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.50.2.tgz#a018de66209051dad0c58e689e080326c3dd15b0"
  integrity sha1-oBjeZiCQUdrQxY5onggDJsPdFbA=

"@rollup/rollup-linux-loong64-gnu@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-loong64-gnu/download/@rollup/rollup-linux-loong64-gnu-4.50.2.tgz#6e514f09988615e0c98fa5a34a88a30fec64d969"
  integrity sha1-blFPCZiGFeDJj6WjSoijD+xk2Wk=

"@rollup/rollup-linux-ppc64-gnu@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-ppc64-gnu/download/@rollup/rollup-linux-ppc64-gnu-4.50.2.tgz#9b2efebc7b4a1951e684a895fdee0fef26319e0d"
  integrity sha1-my7+vHtKGVHmhKiV/e4P7yYxng0=

"@rollup/rollup-linux-riscv64-gnu@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.50.2.tgz#a7104270e93d75789d1ba857b2c68ddf61f24f68"
  integrity sha1-pxBCcOk9dXidG6hXssaN32HyT2g=

"@rollup/rollup-linux-riscv64-musl@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-musl/download/@rollup/rollup-linux-riscv64-musl-4.50.2.tgz#42d153f734a7b9fcacd764cc9bee6c207dca4db6"
  integrity sha1-QtFT9zSnufys12TMm+5sIH3KTbY=

"@rollup/rollup-linux-s390x-gnu@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.50.2.tgz#826ad73099f6fd57c083dc5329151b25404bc67d"
  integrity sha1-gmrXMJn2/VfAg9xTKRUbJUBLxn0=

"@rollup/rollup-linux-x64-gnu@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.50.2.tgz#b9ec17bf0ca3f737d0895fca2115756674342142"
  integrity sha1-uewXvwyj9zfQiV/KIRV1ZnQ0IUI=

"@rollup/rollup-linux-x64-musl@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.50.2.tgz#29fe0adb45a1d99042f373685efbac9cdd5354d9"
  integrity sha1-Kf4K20Wh2ZBC83NoXvusnN1TVNk=

"@rollup/rollup-openharmony-arm64@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-openharmony-arm64/download/@rollup/rollup-openharmony-arm64-4.50.2.tgz#29648f11e202736b74413f823b71e339e3068d60"
  integrity sha1-KWSPEeICc2t0QT+CO3HjOeMGjWA=

"@rollup/rollup-win32-arm64-msvc@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.50.2.tgz#91e7edec80542fd81ab1c2581a91403ac63458ae"
  integrity sha1-keft7IBUL9gascJYGpFAOsY0WK4=

"@rollup/rollup-win32-ia32-msvc@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.50.2.tgz#9b7cd9779f1147a3e8d3ddad432ae64dd222c4e9"
  integrity sha1-m3zZd58RR6Po092tQyrmTdIixOk=

"@rollup/rollup-win32-x64-msvc@4.50.2":
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.50.2.tgz#40ecd1357526fe328c7af704a283ee8533ca7ad6"
  integrity sha1-QOzRNXUm/jKMevcEooPuhTPKetY=

"@tanstack/query-core@5.89.0":
  version "5.89.0"
  resolved "http://r.npm.sankuai.com/@tanstack/query-core/download/@tanstack/query-core-5.89.0.tgz"
  integrity sha1-Z4YvqaoDaUKxkGvFE4URWiu+xFo=

"@tanstack/react-query@^5.48.0":
  version "5.89.0"
  resolved "http://r.npm.sankuai.com/@tanstack/react-query/download/@tanstack/react-query-5.89.0.tgz"
  integrity sha1-rilc7WDRruVVM3pXKtQEXirz0Ao=
  dependencies:
    "@tanstack/query-core" "5.89.0"

"@types/babel__core@^7.20.5":
  version "7.20.5"
  resolved "http://r.npm.sankuai.com/@types/babel__core/download/@types/babel__core-7.20.5.tgz"
  integrity sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  resolved "http://r.npm.sankuai.com/@types/babel__generator/download/@types/babel__generator-7.27.0.tgz"
  integrity sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "http://r.npm.sankuai.com/@types/babel__template/download/@types/babel__template-7.4.4.tgz"
  integrity sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@types/babel__traverse/download/@types/babel__traverse-7.28.0.tgz"
  integrity sha1-B9cT1szg0mXJhJ2wy+YtP2Hzb3Q=
  dependencies:
    "@babel/types" "^7.28.2"

"@types/d3-array@^3.0.3":
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/@types/d3-array/download/@types/d3-array-3.2.1.tgz"
  integrity sha1-H2ZY49IAbE/OrFP95GQWaFn4uMU=

"@types/d3-color@*":
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/@types/d3-color/download/@types/d3-color-3.1.3.tgz"
  integrity sha1-NoyWGhjech2oIA6AvzlD+1MTavI=

"@types/d3-ease@^3.0.0":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@types/d3-ease/download/@types/d3-ease-3.0.2.tgz"
  integrity sha1-4o2xv7+mFwdvd3DdHZpI6qO2xRs=

"@types/d3-interpolate@^3.0.1":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/d3-interpolate/download/@types/d3-interpolate-3.0.4.tgz"
  integrity sha1-QSuQ6EhwKF8v+KhGxutgNE8SpBw=
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*":
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/@types/d3-path/download/@types/d3-path-3.1.1.tgz"
  integrity sha1-9jKzgMOsoduo40qgSbzWpK8j34o=

"@types/d3-scale@^4.0.2":
  version "4.0.9"
  resolved "http://r.npm.sankuai.com/@types/d3-scale/download/@types/d3-scale-4.0.9.tgz"
  integrity sha1-V6L3ByQub+Hega17/Myq9gYXmvs=
  dependencies:
    "@types/d3-time" "*"

"@types/d3-shape@^3.1.0":
  version "3.1.7"
  resolved "http://r.npm.sankuai.com/@types/d3-shape/download/@types/d3-shape-3.1.7.tgz"
  integrity sha1-K3tCPcLf5pyMk1luZz43RDNIxVU=
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.0":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/d3-time/download/@types/d3-time-3.0.4.tgz"
  integrity sha1-hHL+7NY5aRRQ3YAA6zPt1EThMj8=

"@types/d3-timer@^3.0.0":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@types/d3-timer/download/@types/d3-timer-3.0.2.tgz"
  integrity sha1-cLvad9wjqnJ0E+IuIUr6Pw6FL3A=

"@types/dompurify@^3.2.0":
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/@types/dompurify/download/@types/dompurify-3.2.0.tgz"
  integrity sha1-VmEL8+QlDfV3RNYfvZVCLgffuEA=
  dependencies:
    dompurify "*"

"@types/estree@1.0.8":
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.8.tgz"
  integrity sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=

"@types/node@^24.5.2":
  version "24.5.2"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-24.5.2.tgz"
  integrity sha1-Us64P1D+D8/fvSqfq22y6efvZEY=
  dependencies:
    undici-types "~7.12.0"

"@types/prop-types@*":
  version "15.7.15"
  resolved "http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.15.tgz"
  integrity sha1-5uWobWAr6spxzlFj+t9fldcJMcc=

"@types/react-dom@^18.2.19":
  version "18.3.7"
  resolved "http://r.npm.sankuai.com/@types/react-dom/download/@types/react-dom-18.3.7.tgz"
  integrity sha1-uJ3fLNg7T+r8xOLqQa/fuVoNGU8=

"@types/react@^18.2.56":
  version "18.3.24"
  resolved "http://r.npm.sankuai.com/@types/react/download/@types/react-18.3.24.tgz"
  integrity sha1-9qWkxhMkLf468NzuK07Ee5LZtr0=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/triple-beam@^1.3.2":
  version "1.3.5"
  resolved "http://r.npm.sankuai.com/@types/triple-beam/download/@types/triple-beam-1.3.5.tgz"
  integrity sha1-dP75/7qhmOuLWIvgKfOLACmcqiw=

"@types/trusted-types@^2.0.7":
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/@types/trusted-types/download/@types/trusted-types-2.0.7.tgz"
  integrity sha1-usywepcLkXB986PoumiWxX6tLRE=

"@types/uuid@^11.0.0":
  version "11.0.0"
  resolved "http://r.npm.sankuai.com/@types/uuid/download/@types/uuid-11.0.0.tgz"
  integrity sha1-9Po0u/KvlBFI7xlzxDYfxDYXlxw=
  dependencies:
    uuid "*"

"@typescript-eslint/eslint-plugin@^8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-8.44.0.tgz"
  integrity sha1-1yv4stMFKv7pGbo484xXE47uA5Y=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.44.0"
    "@typescript-eslint/type-utils" "8.44.0"
    "@typescript-eslint/utils" "8.44.0"
    "@typescript-eslint/visitor-keys" "8.44.0"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@^8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-8.44.0.tgz"
  integrity sha1-BDb74KcvhtM2bS0VfUgFJLCrPyY=
  dependencies:
    "@typescript-eslint/scope-manager" "8.44.0"
    "@typescript-eslint/types" "8.44.0"
    "@typescript-eslint/typescript-estree" "8.44.0"
    "@typescript-eslint/visitor-keys" "8.44.0"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/project-service/download/@typescript-eslint/project-service-8.44.0.tgz"
  integrity sha1-iQYGUdzs3pRudYRB/pTc6292mik=
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.44.0"
    "@typescript-eslint/types" "^8.44.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-8.44.0.tgz"
  integrity sha1-w38eeG/Q5bQGB5hcdpphwkx2HCY=
  dependencies:
    "@typescript-eslint/types" "8.44.0"
    "@typescript-eslint/visitor-keys" "8.44.0"

"@typescript-eslint/tsconfig-utils@8.44.0", "@typescript-eslint/tsconfig-utils@^8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/tsconfig-utils/download/@typescript-eslint/tsconfig-utils-8.44.0.tgz"
  integrity sha1-jAYBNyv4ifBmOgjfABrWZkQqo6g=

"@typescript-eslint/type-utils@8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-8.44.0.tgz"
  integrity sha1-W4dfipYdFbtH33h8v95QuuoxJhM=
  dependencies:
    "@typescript-eslint/types" "8.44.0"
    "@typescript-eslint/typescript-estree" "8.44.0"
    "@typescript-eslint/utils" "8.44.0"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@8.44.0", "@typescript-eslint/types@^8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-8.44.0.tgz"
  integrity sha1-S5FUqxZKC+/yLTIX/w/cjRC86SQ=

"@typescript-eslint/typescript-estree@8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-8.44.0.tgz"
  integrity sha1-4j6ZRsRmz19Tt+Ruzdl4n9gZLao=
  dependencies:
    "@typescript-eslint/project-service" "8.44.0"
    "@typescript-eslint/tsconfig-utils" "8.44.0"
    "@typescript-eslint/types" "8.44.0"
    "@typescript-eslint/visitor-keys" "8.44.0"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-8.44.0.tgz"
  integrity sha1-LAZQoeioMu0VZY58o8e9KBjZLHw=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.44.0"
    "@typescript-eslint/types" "8.44.0"
    "@typescript-eslint/typescript-estree" "8.44.0"

"@typescript-eslint/visitor-keys@8.44.0":
  version "8.44.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-8.44.0.tgz"
  integrity sha1-DZ1WR+AFwv+KzDkdEgirN9CIUKo=
  dependencies:
    "@typescript-eslint/types" "8.44.0"
    eslint-visitor-keys "^4.2.1"

"@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

"@vitejs/plugin-react@^4.2.1":
  version "4.7.0"
  resolved "http://r.npm.sankuai.com/@vitejs/plugin-react/download/@vitejs/plugin-react-4.7.0.tgz"
  integrity sha1-ZHr057t1rTrdV452KtmEuQ9KJLk=
  dependencies:
    "@babel/core" "^7.28.0"
    "@babel/plugin-transform-react-jsx-self" "^7.27.1"
    "@babel/plugin-transform-react-jsx-source" "^7.27.1"
    "@rolldown/pluginutils" "1.0.0-beta.27"
    "@types/babel__core" "^7.20.5"
    react-refresh "^0.17.0"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.9.0:
  version "8.15.0"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.15.0.tgz"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

ajv@^6.12.4:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.2.2"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.2.2.tgz"
  integrity sha1-YCFu6kZNhkWXzigyAAc4oFiWUME=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.3"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.3.tgz"
  integrity sha1-wETV3MUhoHZBNHJZehrLHxA8QEE=

any-promise@^1.0.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.2:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/arg/download/arg-5.0.2.tgz"
  integrity sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-hidden@^1.2.4:
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/aria-hidden/download/aria-hidden-1.2.6.tgz"
  integrity sha1-cwUcmwiBFMeVsepBTpwP/4dP/Bo=
  dependencies:
    tslib "^2.0.0"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz"
  integrity sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.9"
  resolved "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.9.tgz"
  integrity sha1-HwzKoI6Qzbw+tDMhD5A60PF8Pzo=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.24.0"
    es-object-atoms "^1.1.1"
    get-intrinsic "^1.3.0"
    is-string "^1.1.1"
    math-intrinsics "^1.1.0"

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/array.prototype.findlast/download/array.prototype.findlast-1.2.5.tgz"
  integrity sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz"
  integrity sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz"
  integrity sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.4.tgz"
  integrity sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz"
  integrity sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

async-function@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/async-function/download/async-function-1.0.0.tgz"
  integrity sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=

async@^3.2.3:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz"
  integrity sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

autoprefixer@^10.4.19:
  version "10.4.21"
  resolved "http://r.npm.sankuai.com/autoprefixer/download/autoprefixer-10.4.21.tgz"
  integrity sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=
  dependencies:
    browserslist "^4.24.4"
    caniuse-lite "^1.0.30001702"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.1.1"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

axios@^1.6.8:
  version "1.12.2"
  resolved "http://r.npm.sankuai.com/axios/download/axios-1.12.2.tgz"
  integrity sha1-bDBzkBNs96InjQnOxjsTbfxubac=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.4"
    proxy-from-env "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

baseline-browser-mapping@^2.8.3:
  version "2.8.5"
  resolved "http://r.npm.sankuai.com/baseline-browser-mapping/download/baseline-browser-mapping-2.8.5.tgz"
  integrity sha1-MUf+awGgxJzhlS2uvPwgV/xD/ts=

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

brace-expansion@^1.1.7:
  version "1.1.12"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.12.tgz"
  integrity sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.2.tgz"
  integrity sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0, browserslist@^4.24.4:
  version "4.26.2"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.26.2.tgz"
  integrity sha1-fbOzV37JfxFApS20k2ZUkRB4zvM=
  dependencies:
    baseline-browser-mapping "^2.8.3"
    caniuse-lite "^1.0.30001741"
    electron-to-chromium "^1.5.218"
    node-releases "^2.0.21"
    update-browserslist-db "^1.1.3"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/camelcase-css/download/camelcase-css-2.0.1.tgz"
  integrity sha1-7pePaUeRTMMMa0R0G27R338EP9U=

caniuse-lite@^1.0.30001702, caniuse-lite@^1.0.30001741:
  version "1.0.30001743"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001743.tgz"
  integrity sha1-UP+RqZEiCh7i31rwBlDdXDCOp80=

chalk@^4.0.0:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

class-variance-authority@^0.7.0:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/class-variance-authority/download/class-variance-authority-0.7.1.tgz"
  integrity sha1-QAinmKDkVTp4GlesUXfJ+10EN4c=
  dependencies:
    clsx "^2.1.1"

clsx@^2.0.0, clsx@^2.1.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/clsx/download/clsx-2.1.1.tgz"
  integrity sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=

cmdk@^1.0.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/cmdk/download/cmdk-1.1.1.tgz"
  integrity sha1-uFJCcmmcyqN6rwfzaFCzdr89WOU=
  dependencies:
    "@radix-ui/react-compose-refs" "^1.1.1"
    "@radix-ui/react-dialog" "^1.1.6"
    "@radix-ui/react-id" "^1.1.0"
    "@radix-ui/react-primitive" "^2.0.2"

color-convert@^1.9.3:
  version "1.9.3"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.6.0:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/color-string/download/color-string-1.9.1.tgz"
  integrity sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.1.3:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/color/download/color-3.2.1.tgz"
  integrity sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

colorspace@1.1.x:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/colorspace/download/colorspace-1.1.4.tgz"
  integrity sha1-jUQtEYYVL2BFO/gHDNZus2TlkkM=
  dependencies:
    color "^3.1.3"
    text-hex "1.0.x"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^4.0.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-4.1.1.tgz"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

cross-spawn@^7.0.2, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

csstype@^3.0.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

"d3-array@2 - 3", "d3-array@2.10.0 - 3", d3-array@^3.1.6:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/d3-array/download/d3-array-3.2.4.tgz"
  integrity sha1-Ff7DOyN/l6xdfJhtx32ic6jtC7U=
  dependencies:
    internmap "1 - 2"

"d3-color@1 - 3":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-color/download/d3-color-3.1.0.tgz"
  integrity sha1-OVsoM9+scVB/EqwvevI7+BneJOI=

d3-ease@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-ease/download/d3-ease-3.0.1.tgz"
  integrity sha1-llisOKIUDVnTRhYPH2ww/aC9EvQ=

"d3-format@1 - 3":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-format/download/d3-format-3.1.0.tgz"
  integrity sha1-kmDiOijqXLEJ6TshoG4k4uvVVkE=

"d3-interpolate@1.2.0 - 3", d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-interpolate/download/d3-interpolate-3.0.1.tgz"
  integrity sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=
  dependencies:
    d3-color "1 - 3"

d3-path@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-path/download/d3-path-3.1.0.tgz"
  integrity sha1-It+TkDL7WnGuixgA1h3beFHEJSY=

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/d3-scale/download/d3-scale-4.0.2.tgz"
  integrity sha1-grOOjo/3CAdk+Nzsd71L45Nok5Y=
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

d3-shape@^3.1.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/d3-shape/download/d3-shape-3.2.0.tgz"
  integrity sha1-oag5y9m6RfKGdMadf4Vbz5HfxqU=
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/d3-time-format/download/d3-time-format-4.1.0.tgz"
  integrity sha1-erUlelBB0R7LT+cKXH0WoZW7QIo=
  dependencies:
    d3-time "1 - 3"

"d3-time@1 - 3", "d3-time@2.1.1 - 3", d3-time@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-time/download/d3-time-3.1.0.tgz"
  integrity sha1-kxDbVumS48AXXh7zheVF5Iqbtcc=
  dependencies:
    d3-array "2 - 3"

d3-timer@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-timer/download/d3-timer-3.0.1.tgz"
  integrity sha1-YoTSonCChbGrt+IB7aQ4CvNeY7A=

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.2.tgz"
  integrity sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz"
  integrity sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz"
  integrity sha1-BoMH+bcat2274QKROJ4CCFZgYZE=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/date-fns/download/date-fns-3.6.0.tgz"
  integrity sha1-8gyk/pT4t1SVGyQkBnboYYwCBr8=

debug@^4.1.0, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.4.3"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.3.tgz"
  integrity sha1-xq5DLZvZZiWC/OCHCbA4xY6ePWo=
  dependencies:
    ms "^2.1.3"

decimal.js-light@^2.4.1:
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/decimal.js-light/download/decimal.js-light-2.5.1.tgz"
  integrity sha1-E0/TJQjxniCPT7L42sDSYmqGeTQ=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/detect-node-es/download/detect-node-es-1.1.0.tgz"
  integrity sha1-FjrN9kMzDKoLTNfCHn7ndV1vpJM=

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/didyoumean/download/didyoumean-1.2.2.tgz"
  integrity sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=

dlv@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/dlv/download/dlv-1.1.3.tgz"
  integrity sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/dom-helpers/download/dom-helpers-5.2.1.tgz"
  integrity sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dompurify@*:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/dompurify/download/dompurify-3.2.6.tgz"
  integrity sha1-ygQKatK4jiqS3EXzjHn4SnFKHK0=
  optionalDependencies:
    "@types/trusted-types" "^2.0.7"

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

electron-to-chromium@^1.5.218:
  version "1.5.221"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.221.tgz"
  integrity sha1-vZgBSyokdwHE69cTCARI1TlUXXk=

embla-carousel-react@^8.1.5:
  version "8.6.0"
  resolved "http://r.npm.sankuai.com/embla-carousel-react/download/embla-carousel-react-8.6.0.tgz"
  integrity sha1-tzcEKjJ2HDjWYUWTZTs6xhlHe9E=
  dependencies:
    embla-carousel "8.6.0"
    embla-carousel-reactive-utils "8.6.0"

embla-carousel-reactive-utils@8.6.0:
  version "8.6.0"
  resolved "http://r.npm.sankuai.com/embla-carousel-reactive-utils/download/embla-carousel-reactive-utils-8.6.0.tgz"
  integrity sha1-YH8dirmSHJBqVVwgYlGyxttociM=

embla-carousel@8.6.0:
  version "8.6.0"
  resolved "http://r.npm.sankuai.com/embla-carousel/download/embla-carousel-8.6.0.tgz"
  integrity sha1-q87f8r/zaZLqisJ80wCAyltqP1g=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

enabled@2.0.x:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/enabled/download/enabled-2.0.0.tgz"
  integrity sha1-+d2S7C1vS7wNXR5k4h1hzUZl58I=

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9, es-abstract@^1.24.0:
  version "1.24.0"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.24.0.tgz"
  integrity sha1-xEcy0r6wrMHtYN+ECGnjEG568yg=
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.3.0"
    get-proto "^1.0.1"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-negative-zero "^2.0.3"
    is-regex "^1.2.1"
    is-set "^2.0.3"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.1"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.4"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.4"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    stop-iteration-iterator "^1.1.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.19"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/es-iterator-helpers/download/es-iterator-helpers-1.2.1.tgz"
  integrity sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.1.0.tgz"
  integrity sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.3.0.tgz"
  integrity sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

esbuild@^0.21.3:
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.21.5.tgz"
  integrity sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

escalade@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-plugin-react-hooks@^4.6.0:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.6.2.tgz"
  integrity sha1-yCnrBsDm9ISz+7hal+V3hPMoxZY=

eslint-plugin-react-refresh@^0.4.20:
  version "0.4.20"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-refresh/download/eslint-plugin-react-refresh-0.4.20.tgz"
  integrity sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=

eslint-plugin-react@^7.33.2:
  version "7.37.5"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react/download/eslint-plugin-react-7.37.5.tgz"
  integrity sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.9"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-visitor-keys@^4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.1.tgz"
  integrity sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=

eslint@^8.56.0:
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-8.57.1.tgz"
  integrity sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.2:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

eventemitter3@^4.0.1:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-4.0.7.tgz"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-equals@^5.0.1:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/fast-equals/download/fast-equals-5.2.2.tgz"
  integrity sha1-iF17+wefrAzg6EUDdLzinpt0JIQ=

fast-glob@^3.3.2:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fecha@^4.2.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/fecha/download/fecha-4.2.3.tgz"
  integrity sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-stream-rotator@^0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/file-stream-rotator/download/file-stream-rotator-0.6.1.tgz"
  integrity sha1-AHAZ5zWyYrtsbwGX5Y5ch8uWzsM=
  dependencies:
    moment "^2.29.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

fn.name@1.x.x:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/fn.name/download/fn.name-1.1.0.tgz"
  integrity sha1-JsrYAXlnrqhzG8QpYdBKPVmIrMw=

follow-redirects@^1.15.6:
  version "1.15.11"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.11.tgz"
  integrity sha1-d31z1yqS+OxNLkEOtHNSpWuOg0A=

for-each@^0.3.3, for-each@^0.3.5:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/for-each/download/for-each-0.3.5.tgz"
  integrity sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/foreground-child/download/foreground-child-3.3.1.tgz"
  integrity sha1-Mujp7Rtoo0l777msK2rfkqY4V28=
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

form-data@^4.0.4:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.4.tgz"
  integrity sha1-eEzczgZpqdaOlNEaxO6pgIjt0sQ=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "http://r.npm.sankuai.com/fraction.js/download/fraction.js-4.3.7.tgz"
  integrity sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=

framer-motion@^11.3.9:
  version "11.18.2"
  resolved "http://r.npm.sankuai.com/framer-motion/download/framer-motion-11.18.2.tgz"
  integrity sha1-DGvQVnf0z9OzverU617N1e0kVxg=
  dependencies:
    motion-dom "^11.18.1"
    motion-utils "^11.18.1"
    tslib "^2.4.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.8.tgz"
  integrity sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-nonce@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-nonce/download/get-nonce-1.0.1.tgz"
  integrity sha1-/fPwJ4Bzgg0s6UJsGPB0gbHgzfM=

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.1.0.tgz"
  integrity sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.4.5"
  resolved "http://r.npm.sankuai.com/glob/download/glob-10.4.5.tgz"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.19.0:
  version "13.24.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.1.0.tgz"
  integrity sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/has-proto/download/has-proto-1.2.0.tgz"
  integrity sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

html-to-image@^1.11.11:
  version "1.11.13"
  resolved "http://r.npm.sankuai.com/html-to-image/download/html-to-image-1.11.13.tgz"
  integrity sha1-rbyYnJk7eq+QtinAys+DPbhNX0M=

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.0:
  version "7.0.5"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-7.0.5.tgz"
  integrity sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=

import-fresh@^3.2.1:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

input-otp@^1.2.4:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/input-otp/download/input-otp-1.4.2.tgz"
  integrity sha1-9NPVh9D2QXKeVQKbO4xIcIR/Twc=

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.1.0.tgz"
  integrity sha1-HqyRdilH0vcFa8g42T4TsulgSWE=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

"internmap@1 - 2":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/internmap/download/internmap-2.0.3.tgz"
  integrity sha1-ZoXyN1XkPFJOJR0py8lySOMGEAk=

invariant@^2.2.4:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/invariant/download/invariant-2.2.4.tgz"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.5.tgz"
  integrity sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-async-function@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-async-function/download/is-async-function-2.1.1.tgz"
  integrity sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.1.0.tgz"
  integrity sha1-3aejRF31ekJYPbQihoLrp8QXBnI=
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.2.2.tgz"
  integrity sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.13.0, is-core-module@^2.16.0:
  version "2.16.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.2.tgz"
  integrity sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.1.0.tgz"
  integrity sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz"
  integrity sha1-7v3NxslN3QZ02chYh7+T+USpfJA=
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.10:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz"
  integrity sha1-vz7tqTEgE5T1e126KAD5GiODCco=
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-map@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-map/download/is-map-2.0.3.tgz"
  integrity sha1-7elrf+HicLPERl46RlZYdkkm1i4=

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.3.tgz"
  integrity sha1-ztkDoCespjgbd3pXQwadc3akl0c=

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.1.1.tgz"
  integrity sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-3.0.3.tgz"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-regex@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz"
  integrity sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-set/download/is-set-2.0.3.tgz"
  integrity sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz"
  integrity sha1-m2eES9m38ka6BwjDqT40Jpx3T28=
  dependencies:
    call-bound "^1.0.3"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-string/download/is-string-1.1.1.tgz"
  integrity sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.1.1.tgz"
  integrity sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/is-weakmap/download/is-weakmap-2.0.2.tgz"
  integrity sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=

is-weakref@^1.0.2, is-weakref@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.1.1.tgz"
  integrity sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-weakset/download/is-weakset-2.0.4.tgz"
  integrity sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

isarray@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

iterator.prototype@^1.1.4:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/iterator.prototype/download/iterator.prototype-1.1.5.tgz"
  integrity sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/jackspeak/download/jackspeak-3.4.3.tgz"
  integrity sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jiti@^1.21.6:
  version "1.21.7"
  resolved "http://r.npm.sankuai.com/jiti/download/jiti-1.21.7.tgz"
  integrity sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.5"
  resolved "http://r.npm.sankuai.com/jsx-ast-utils/download/jsx-ast-utils-3.3.5.tgz"
  integrity sha1-R2a9BajioRryIr7NGeFVdeUqhTo=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kuler@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/kuler/download/kuler-2.0.0.tgz"
  integrity sha1-4sVwo4ADiPtEQH6FFTHB1nCwYbM=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/lilconfig/download/lilconfig-3.1.3.tgz"
  integrity sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

logform@^2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/logform/download/logform-2.7.0.tgz"
  integrity sha1-z8qXUo7ykPLhJaCDloBQArLQYNE=
  dependencies:
    "@colors/colors" "1.6.0"
    "@types/triple-beam" "^1.3.2"
    fecha "^4.2.0"
    ms "^2.1.1"
    safe-stable-stringify "^2.3.1"
    triple-beam "^1.3.0"

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-10.4.3.tgz"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lucide-react@^0.417.0:
  version "0.417.0"
  resolved "http://r.npm.sankuai.com/lucide-react/download/lucide-react-0.417.0.tgz"
  integrity sha1-yqJP2+sz1iq9ov2eZDN/zIUiR3c=

marked@^16.3.0:
  version "16.3.0"
  resolved "http://r.npm.sankuai.com/marked/download/marked-16.3.0.tgz"
  integrity sha1-L1E4kfhn1u3EdytKAm25zDMeuU8=

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

merge2@^1.3.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.12:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-9.0.5.tgz"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-7.1.2.tgz"
  integrity sha1-k6libOXl5mvU24aEnnUV6SNApwc=

moment@^2.29.1:
  version "2.30.1"
  resolved "http://r.npm.sankuai.com/moment/download/moment-2.30.1.tgz"
  integrity sha1-+MkcB7enhuMMWZJt9TC06slpdK4=

motion-dom@^11.18.1:
  version "11.18.1"
  resolved "http://r.npm.sankuai.com/motion-dom/download/motion-dom-11.18.1.tgz"
  integrity sha1-5/7Xt9xq4SI+8czinuVL7IJtw/I=
  dependencies:
    motion-utils "^11.18.1"

motion-utils@^11.18.1:
  version "11.18.1"
  resolved "http://r.npm.sankuai.com/motion-utils/download/motion-utils-11.18.1.tgz"
  integrity sha1-ZxInZpgz6ZHFWBPPM3iZ9BMn21s=

ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mz@^2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/mz/download/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.11:
  version "3.3.11"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

next-themes@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/next-themes/download/next-themes-0.3.0.tgz"
  integrity sha1-tNKoZhN6Z9QlZLB/Oj5yDi/zhxo=

node-releases@^2.0.21:
  version "2.0.21"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.21.tgz"
  integrity sha1-9ZsBi8AEgES+LUxMBOTIsYFgiUw=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/normalize-range/download/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/object-hash/download/object-hash-3.0.0.tgz"
  integrity sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=

object-inspect@^1.13.3, object-inspect@^1.13.4:
  version "1.13.4"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz"
  integrity sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.9:
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/object.entries/download/object.entries-1.1.9.tgz"
  integrity sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.values@^1.1.6, object.values@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.2.1.tgz"
  integrity sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

once@^1.3.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

one-time@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/one-time/download/one-time-1.0.0.tgz"
  integrity sha1-4GvBdK7SFO1Y7e3lc7Qzu/gny0U=
  dependencies:
    fn.name "1.x.x"

optionator@^0.9.3:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

own-keys@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/own-keys/download/own-keys-1.0.1.tgz"
  integrity sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz"
  integrity sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "http://r.npm.sankuai.com/path-scurry/download/path-scurry-1.11.1.tgz"
  integrity sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

pify@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pirates@^4.0.1:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/pirates/download/pirates-4.0.7.tgz"
  integrity sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz"
  integrity sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "http://r.npm.sankuai.com/postcss-import/download/postcss-import-15.1.0.tgz"
  integrity sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-js/download/postcss-js-4.0.1.tgz"
  integrity sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-load-config/download/postcss-load-config-4.0.2.tgz"
  integrity sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/postcss-nested/download/postcss-nested-6.2.0.tgz"
  integrity sha1-TC0iq18gucth4sXFkVlQeE0GgTE=
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz"
  integrity sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.4.38, postcss@^8.4.43, postcss@^8.4.47:
  version "8.5.6"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.6.tgz"
  integrity sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prop-types@^15.5.7, prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://r.npm.sankuai.com/prop-types/download/prop-types-15.8.1.tgz"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

punycode@^2.1.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

react-day-picker@^8.10.1:
  version "8.10.1"
  resolved "http://r.npm.sankuai.com/react-day-picker/download/react-day-picker-8.10.1.tgz"
  integrity sha1-R2LsKYhlkZuT7Am6aWIVgINbjoA=

react-dom@^18.2.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-dom/download/react-dom-18.3.1.tgz"
  integrity sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-hook-form@^7.52.0:
  version "7.62.0"
  resolved "http://r.npm.sankuai.com/react-hook-form/download/react-hook-form-7.62.0.tgz"
  integrity sha1-LYHhPCxrbWNlSORAgYNBynUyGNA=

react-is@^16.13.1:
  version "16.13.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-16.13.1.tgz"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^18.3.1:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-refresh@^0.17.0:
  version "0.17.0"
  resolved "http://r.npm.sankuai.com/react-refresh/download/react-refresh-0.17.0.tgz"
  integrity sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=

react-remove-scroll-bar@^2.3.7:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/react-remove-scroll-bar/download/react-remove-scroll-bar-2.3.8.tgz"
  integrity sha1-mcIPkI7kZ7OFtoo0abSj51ABIiM=
  dependencies:
    react-style-singleton "^2.2.2"
    tslib "^2.0.0"

react-remove-scroll@^2.6.3:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/react-remove-scroll/download/react-remove-scroll-2.7.1.tgz"
  integrity sha1-0hAdQU9tgdfTvwM/PBy0eFeJ91M=
  dependencies:
    react-remove-scroll-bar "^2.3.7"
    react-style-singleton "^2.2.3"
    tslib "^2.1.0"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.3"

react-resizable-panels@^2.0.19:
  version "2.1.9"
  resolved "http://r.npm.sankuai.com/react-resizable-panels/download/react-resizable-panels-2.1.9.tgz"
  integrity sha1-h0hHcQ9PEi33SbXwjr6ccqHjOMo=

react-router-dom@^6.23.1:
  version "6.30.1"
  resolved "http://r.npm.sankuai.com/react-router-dom/download/react-router-dom-6.30.1.tgz"
  integrity sha1-2iWAwnLdthMl5DVHhWa+lWOkojc=
  dependencies:
    "@remix-run/router" "1.23.0"
    react-router "6.30.1"

react-router@6.30.1:
  version "6.30.1"
  resolved "http://r.npm.sankuai.com/react-router/download/react-router-6.30.1.tgz"
  integrity sha1-7LO4g8m6bb9dMZ3byZZ0f0q59MM=
  dependencies:
    "@remix-run/router" "1.23.0"

react-smooth@^4.0.4:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/react-smooth/download/react-smooth-4.0.4.tgz"
  integrity sha1-pYdfi7YZY8phuBnO3FadwkU4lLQ=
  dependencies:
    fast-equals "^5.0.1"
    prop-types "^15.8.1"
    react-transition-group "^4.4.5"

react-sortable-hoc@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/react-sortable-hoc/download/react-sortable-hoc-2.0.0.tgz"
  integrity sha1-9ngNiqS5IqIfPnVK9ULwMmdweLc=
  dependencies:
    "@babel/runtime" "^7.2.0"
    invariant "^2.2.4"
    prop-types "^15.5.7"

react-style-singleton@^2.2.2, react-style-singleton@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/react-style-singleton/download/react-style-singleton-2.2.3.tgz"
  integrity sha1-QmVgi+aaTXDP4wR/LGyIssOs44g=
  dependencies:
    get-nonce "^1.0.0"
    tslib "^2.0.0"

react-transition-group@^4.4.5:
  version "4.4.5"
  resolved "http://r.npm.sankuai.com/react-transition-group/download/react-transition-group-4.4.5.tgz"
  integrity sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@^18.2.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react/download/react-18.3.1.tgz"
  integrity sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=
  dependencies:
    loose-envify "^1.1.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/read-cache/download/read-cache-1.0.0.tgz"
  integrity sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=
  dependencies:
    pify "^2.3.0"

readable-stream@^3.4.0, readable-stream@^3.6.2:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

recharts-scale@^0.4.4:
  version "0.4.5"
  resolved "http://r.npm.sankuai.com/recharts-scale/download/recharts-scale-0.4.5.tgz"
  integrity sha1-CWknHxTnMuZC/MW9SrJw1uh90dk=
  dependencies:
    decimal.js-light "^2.4.1"

recharts@^2.12.7:
  version "2.15.4"
  resolved "http://r.npm.sankuai.com/recharts/download/recharts-2.15.4.tgz"
  integrity sha1-DtPmbAhDvPLZ+aFyyvl7HQUSel8=
  dependencies:
    clsx "^2.0.0"
    eventemitter3 "^4.0.1"
    lodash "^4.17.21"
    react-is "^18.3.1"
    react-smooth "^4.0.4"
    recharts-scale "^0.4.4"
    tiny-invariant "^1.3.1"
    victory-vendor "^36.6.8"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.10.tgz"
  integrity sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regexp.prototype.flags@^1.5.3, regexp.prototype.flags@^1.5.4:
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.4.tgz"
  integrity sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve@^1.1.7, resolve@^1.22.8:
  version "1.22.10"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-2.0.0-next.5.tgz"
  integrity sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rollup@^4.20.0:
  version "4.50.2"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-4.50.2.tgz"
  integrity sha1-k42Jg5STnzOG0eNn7mQQp5a48mg=
  dependencies:
    "@types/estree" "1.0.8"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.50.2"
    "@rollup/rollup-android-arm64" "4.50.2"
    "@rollup/rollup-darwin-arm64" "4.50.2"
    "@rollup/rollup-darwin-x64" "4.50.2"
    "@rollup/rollup-freebsd-arm64" "4.50.2"
    "@rollup/rollup-freebsd-x64" "4.50.2"
    "@rollup/rollup-linux-arm-gnueabihf" "4.50.2"
    "@rollup/rollup-linux-arm-musleabihf" "4.50.2"
    "@rollup/rollup-linux-arm64-gnu" "4.50.2"
    "@rollup/rollup-linux-arm64-musl" "4.50.2"
    "@rollup/rollup-linux-loong64-gnu" "4.50.2"
    "@rollup/rollup-linux-ppc64-gnu" "4.50.2"
    "@rollup/rollup-linux-riscv64-gnu" "4.50.2"
    "@rollup/rollup-linux-riscv64-musl" "4.50.2"
    "@rollup/rollup-linux-s390x-gnu" "4.50.2"
    "@rollup/rollup-linux-x64-gnu" "4.50.2"
    "@rollup/rollup-linux-x64-musl" "4.50.2"
    "@rollup/rollup-openharmony-arm64" "4.50.2"
    "@rollup/rollup-win32-arm64-msvc" "4.50.2"
    "@rollup/rollup-win32-ia32-msvc" "4.50.2"
    "@rollup/rollup-win32-x64-msvc" "4.50.2"
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.3.tgz"
  integrity sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/safe-push-apply/download/safe-push-apply-1.0.0.tgz"
  integrity sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz"
  integrity sha1-f4fftnoxUHguqvGFg/9dFxGsEME=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

safe-stable-stringify@^2.3.1:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/safe-stable-stringify/download/safe-stable-stringify-2.5.0.tgz"
  integrity sha1-TKL444XygxxDKnGbEIo7969Cod0=

scheduler@^0.23.2:
  version "0.23.2"
  resolved "http://r.npm.sankuai.com/scheduler/download/scheduler-0.23.2.tgz"
  integrity sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=
  dependencies:
    loose-envify "^1.1.0"

semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.6.0:
  version "7.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/set-proto/download/set-proto-1.0.0.tgz"
  integrity sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

sonner@^1.5.0:
  version "1.7.4"
  resolved "http://r.npm.sankuai.com/sonner/download/sonner-1.7.4.tgz"
  integrity sha1-TDmCDbhmI4AKFxFciXB5aqhiEzo=

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

stack-trace@0.0.x:
  version "0.0.10"
  resolved "http://r.npm.sankuai.com/stack-trace/download/stack-trace-0.0.10.tgz"
  integrity sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=

stop-iteration-iterator@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/stop-iteration-iterator/download/stop-iteration-iterator-1.1.0.tgz"
  integrity sha1-9IH/cKVI9hJNAxLDqhTL+nqlQq0=
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  resolved "http://r.npm.sankuai.com/string.prototype.matchall/download/string.prototype.matchall-4.0.12.tgz"
  integrity sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/string.prototype.repeat/download/string.prototype.repeat-1.0.0.tgz"
  integrity sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  resolved "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz"
  integrity sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz"
  integrity sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.2"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.2.tgz"
  integrity sha1-Eyh1q95njH6o1pFTPy5+Irt0Tbo=
  dependencies:
    ansi-regex "^6.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

sucrase@^3.35.0:
  version "3.35.0"
  resolved "http://r.npm.sankuai.com/sucrase/download/sucrase-3.35.0.tgz"
  integrity sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

tailwind-merge@^2.2.1:
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/tailwind-merge/download/tailwind-merge-2.6.0.tgz"
  integrity sha1-rF+34ieRDAONRY85a3QA2ToxQtU=

tailwindcss-animate@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/tailwindcss-animate/download/tailwindcss-animate-1.0.7.tgz"
  integrity sha1-MYtpLExCZ2zJ5nsZt4d1dCOIvvQ=

tailwindcss@^3.4.4:
  version "3.4.17"
  resolved "http://r.npm.sankuai.com/tailwindcss/download/tailwindcss-3.4.17.tgz"
  integrity sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

text-hex@1.0.x:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/text-hex/download/text-hex-1.0.0.tgz"
  integrity sha1-adycGxdEbueakr9biEu0uRJ1BvU=

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/thenify-all/download/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/thenify/download/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

tiny-invariant@^1.3.1:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/tiny-invariant/download/tiny-invariant-1.3.3.tgz"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

triple-beam@^1.3.0, triple-beam@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/triple-beam/download/triple-beam-1.4.1.tgz"
  integrity sha1-b95wJx3G5dc8oMOyTi2Sr7dEGYQ=

ts-api-utils@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/ts-api-utils/download/ts-api-utils-2.1.0.tgz"
  integrity sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "http://r.npm.sankuai.com/ts-interface-checker/download/ts-interface-checker-0.1.13.tgz"
  integrity sha1-eE/T1nlyK8EDsbS4AwvN212yppk=

tslib@^2.0.0, tslib@^2.1.0, tslib@^2.4.0:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz"
  integrity sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz"
  integrity sha1-hAegT314aE89JSqhoUPSt3tBYM4=
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz"
  integrity sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.7.tgz"
  integrity sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript@^5.9.2:
  version "5.9.2"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-5.9.2.tgz"
  integrity sha1-2TRQzd7FFUotXKvjuBArgzFvsqY=

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.1.0.tgz"
  integrity sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~7.12.0:
  version "7.12.0"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-7.12.0.tgz"
  integrity sha1-FcXHR1wqO6MGWVKfXNtGdLYi+vs=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

use-callback-ref@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/use-callback-ref/download/use-callback-ref-1.3.3.tgz"
  integrity sha1-mNn6sGcHWEHFssaFIJDV0P6r4r8=
  dependencies:
    tslib "^2.0.0"

use-sidecar@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/use-sidecar/download/use-sidecar-1.1.3.tgz"
  integrity sha1-EOf9iX0TC4luLFRsY6XoIz0A79s=
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.5.0.tgz"
  integrity sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=

util-deprecate@^1.0.1, util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

uuid@*:
  version "13.0.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-13.0.0.tgz"
  integrity sha1-Jj3DQbGbTXVeuP42t42VprZXB+g=

vaul@^0.9.1:
  version "0.9.9"
  resolved "http://r.npm.sankuai.com/vaul/download/vaul-0.9.9.tgz"
  integrity sha1-/wdcPLphk9SFm7bxsJ78zgSc+BI=
  dependencies:
    "@radix-ui/react-dialog" "^1.1.1"

victory-vendor@^36.6.8:
  version "36.9.2"
  resolved "http://r.npm.sankuai.com/victory-vendor/download/victory-vendor-36.9.2.tgz"
  integrity sha1-ZosCpEj6TqD3iNv0Iot+ZGaf+AE=
  dependencies:
    "@types/d3-array" "^3.0.3"
    "@types/d3-ease" "^3.0.0"
    "@types/d3-interpolate" "^3.0.1"
    "@types/d3-scale" "^4.0.2"
    "@types/d3-shape" "^3.1.0"
    "@types/d3-time" "^3.0.0"
    "@types/d3-timer" "^3.0.0"
    d3-array "^3.1.6"
    d3-ease "^3.0.1"
    d3-interpolate "^3.0.1"
    d3-scale "^4.0.2"
    d3-shape "^3.1.0"
    d3-time "^3.0.0"
    d3-timer "^3.0.1"

vite@5.4.11:
  version "5.4.11"
  resolved "http://r.npm.sankuai.com/vite/download/vite-5.4.11.tgz"
  integrity sha1-O0Fc1K7XgaNWwd5anrr7g3cV9uU=
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.43"
    rollup "^4.20.0"
  optionalDependencies:
    fsevents "~2.3.3"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz"
  integrity sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/which-builtin-type/download/which-builtin-type-1.2.1.tgz"
  integrity sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/which-collection/download/which-collection-1.0.2.tgz"
  integrity sha1-Yn73YkOSChB+fOjpYZHevksWwqA=
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.19:
  version "1.1.19"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.19.tgz"
  integrity sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

winston-daily-rotate-file@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/winston-daily-rotate-file/download/winston-daily-rotate-file-5.0.0.tgz"
  integrity sha1-jNlIAAJUkOR8AOyJK2VaWCH0Jm0=
  dependencies:
    file-stream-rotator "^0.6.1"
    object-hash "^3.0.0"
    triple-beam "^1.4.1"
    winston-transport "^4.7.0"

winston-transport@^4.7.0, winston-transport@^4.9.0:
  version "4.9.0"
  resolved "http://r.npm.sankuai.com/winston-transport/download/winston-transport-4.9.0.tgz"
  integrity sha1-O7o0XeECl2VOpvM1GUJFYAA7O/k=
  dependencies:
    logform "^2.7.0"
    readable-stream "^3.6.2"
    triple-beam "^1.3.0"

winston@^3.17.0:
  version "3.17.0"
  resolved "http://r.npm.sankuai.com/winston/download/winston-3.17.0.tgz"
  integrity sha1-dLhmXOm06nsp0JIs/M+FKgihFCM=
  dependencies:
    "@colors/colors" "^1.6.0"
    "@dabh/diagnostics" "^2.0.2"
    async "^3.2.3"
    is-stream "^2.0.0"
    logform "^2.7.0"
    one-time "^1.0.0"
    readable-stream "^3.4.0"
    safe-stable-stringify "^2.3.1"
    stack-trace "0.0.x"
    triple-beam "^1.3.0"
    winston-transport "^4.9.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yaml@^2.3.4:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-2.8.1.tgz"
  integrity sha1-GHCqArYx9+gyi5P4vFdPrF1sTXk=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

zod@^3.23.8:
  version "3.25.76"
  resolved "http://r.npm.sankuai.com/zod/download/zod-3.25.76.tgz"
  integrity sha1-JoQcP2/SKmonYOfMtxkXl2hHHjQ=

zustand@^5.0.8:
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/zustand/download/zustand-5.0.8.tgz"
  integrity sha1-uZigwIjHAnog8nCRQakcsHrFf4o=
