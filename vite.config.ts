import {fileURLToPath, URL} from 'url';
import {defineConfig} from 'vite';
import {dirname, resolve} from 'path';
import {tmpdir} from 'os';
import {devLogger} from '@meituan-nocode/vite-plugin-dev-logger';
import {devHtmlTransformer, prodHtmlTransformer,} from '@meituan-nocode/vite-plugin-nocode-html-transformer';
import react from '@vitejs/plugin-react';

const CHAT_VARIABLE = process.env.CHAT_VARIABLE || '';
const PUBLIC_PATH = process.env.PUBLIC_PATH || '';
const PUBLIC_URL = process.env.PUBLIC_URL || '';
const VITE_TALOS = process.env.VITE_TALOS === 'true';

const isProdEnv = process.env.NODE_ENV === 'production';

// 根据 VITE_TALOS 决定 publicPath 的获取方式
const publicPath = VITE_TALOS
  ? (PUBLIC_URL ? (PUBLIC_URL.endsWith('/') ? PUBLIC_URL : PUBLIC_URL + '/') : '/')
  : (isProdEnv && CHAT_VARIABLE)
    ? PUBLIC_PATH + '/' + CHAT_VARIABLE
    : PUBLIC_PATH + '/';
const outDir = (isProdEnv && CHAT_VARIABLE) ? 'build/' + CHAT_VARIABLE : 'build';
const plugins = isProdEnv
  ? CHAT_VARIABLE
    ? [react(), prodHtmlTransformer(CHAT_VARIABLE)]
    : [react()]
  : [
      devLogger({
        dirname: resolve(tmpdir(), '.nocode-dev-logs'),
        maxFiles: '3d',
      }),
      react(),
      devHtmlTransformer(CHAT_VARIABLE, 'internal'),
    ];

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: '::',
    port: 8080,
    hmr: {
      overlay: false,
    },
    // Vite 5.x 默认支持 SPA 路由回退，无需额外配置
  },
  plugins,
  base: publicPath,
  build: {
    outDir,
  },
  resolve: {
    alias: [
      {
        find: '@',
        replacement: fileURLToPath(new URL('./src', import.meta.url)),
      },
      {
        find: 'lib',
        replacement: resolve(dirname(fileURLToPath(import.meta.url)), 'lib'),
      },
    ],
  },
});
