/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: ["class"],
    content: [
        "./pages/**/*.{js,jsx,ts,tsx}",
        "./components/**/*.{js,jsx,ts,tsx}",
        "./app/**/*.{js,jsx,ts,tsx}",
        "./src/**/*.{js,jsx,ts,tsx}",
        "./index.html",
    ],
    prefix: "",
    theme: {
        container: {
            center: true,
            padding: "2rem",
            screens: {
                "2xl": "1400px",
            },
        },
        screens: {
            'xs': '475px',
            'sm': '640px',
            'md': '768px',
            'lg': '1024px',
            'xl': '1280px',
            '2xl': '1400px',
            '3xl': '1600px',
            '4xl': '1920px',
            // 超宽屏幕
            'ultrawide': '2560px',
        },
        extend: {
            colors: {
                border: "hsl(var(--border))",
                input: "hsl(var(--input))",
                ring: "hsl(var(--ring))",
                background: "hsl(var(--background))",
                foreground: "hsl(var(--foreground))",
                primary: {
                    DEFAULT: "hsl(var(--primary))",
                    foreground: "hsl(var(--primary-foreground))",
                },
                secondary: {
                    DEFAULT: "hsl(var(--secondary))",
                    foreground: "hsl(var(--secondary-foreground))",
                },
                destructive: {
                    DEFAULT: "hsl(var(--destructive))",
                    foreground: "hsl(var(--destructive-foreground))",
                },
                muted: {
                    DEFAULT: "hsl(var(--muted))",
                    foreground: "hsl(var(--muted-foreground))",
                },
                accent: {
                    DEFAULT: "hsl(var(--accent))",
                    foreground: "hsl(var(--accent-foreground))",
                },
                popover: {
                    DEFAULT: "hsl(var(--popover))",
                    foreground: "hsl(var(--popover-foreground))",
                },
                card: {
                    DEFAULT: "hsl(var(--card))",
                    foreground: "hsl(var(--card-foreground))",
                },
                // 聊天界面专用颜色
                chat: {
                    user: "hsl(220 14% 96%)",
                    assistant: "hsl(0 0% 100%)",
                    "user-dark": "hsl(217 33% 17%)",
                    "assistant-dark": "hsl(224 71% 4%)",
                },
                // 状态颜色
                status: {
                    success: "hsl(142 76% 36%)",
                    warning: "hsl(38 92% 50%)",
                    error: "hsl(0 84% 60%)",
                    info: "hsl(217 91% 60%)",
                    pending: "hsl(43 74% 66%)",
                },
                // 链路追踪颜色
                trace: {
                    http: "hsl(217 91% 60%)",
                    db: "hsl(142 76% 36%)",
                    redis: "hsl(0 84% 60%)",
                    mq: "hsl(271 81% 56%)",
                    thrift: "hsl(38 92% 50%)",
                }
            },
            borderRadius: {
                lg: "var(--radius)",
                md: "calc(var(--radius) - 2px)",
                sm: "calc(var(--radius) - 4px)",
            },
            spacing: {
                '18': '4.5rem',
                '88': '22rem',
                '128': '32rem',
                '144': '36rem',
            },
            maxWidth: {
                '8xl': '88rem',
                '9xl': '96rem',
            },
            keyframes: {
                "accordion-down": {
                    from: {height: "0"},
                    to: {height: "var(--radix-accordion-content-height)"},
                },
                "accordion-up": {
                    from: {height: "var(--radix-accordion-content-height)"},
                    to: {height: "0"},
                },
                "fade-in": {
                    from: {opacity: "0", transform: "translateY(10px)"},
                    to: {opacity: "1", transform: "translateY(0)"},
                },
                "pulse-dot": {
                    "0%, 100%": {opacity: "0.4"},
                    "50%": {opacity: "1"},
                },
                "typing": {
                    "0%": {width: "0"},
                    "100%": {width: "100%"},
                }
            },
            animation: {
                "accordion-down": "accordion-down 0.2s ease-out",
                "accordion-up": "accordion-up 0.2s ease-out",
                "fade-in": "fade-in 0.3s ease-out",
                "pulse-dot": "pulse-dot 1.5s ease-in-out infinite",
                "typing": "typing 2s steps(20) infinite",
            },
        },
    },
    plugins: [require("tailwindcss-animate")],
};
