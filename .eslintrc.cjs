module.exports = {
    root: true,
    env: {
        browser: true,
        es2020: true,
        node: true
    },
    extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:react-hooks/recommended',
    ],
    ignorePatterns: ['dist', 'build', '.eslintrc.cjs', '*.cjs'],
    parser: '@typescript-eslint/parser',
    plugins: ['react-refresh', '@typescript-eslint'],
    globals: {
        React: 'readonly',
        process: 'readonly',
        __dirname: 'readonly',
    },
    rules: {
        'react-refresh/only-export-components': 'off',
        'react-hooks/exhaustive-deps': 'off',
        '@typescript-eslint/no-unused-vars': ['error', {argsIgnorePattern: '^_'}],
        '@typescript-eslint/no-explicit-any': 'warn',
        'no-unused-vars': 'off', // 使用 TypeScript 版本
        'no-undef': 'off', // TypeScript 处理
        'no-redeclare': 'off', // TypeScript 处理
        'no-prototype-builtins': 'off',
        'no-constant-condition': 'off',
    },
}

