import React from "react";
import {Toaster} from "@/components/ui/sonner";
import {TooltipProvider} from "@/components/ui/tooltip";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import {BrowserRouter, Route, Routes} from "react-router-dom";
import {navItems} from "./nav-items";
import NotFound from "./pages/NotFound";
import SimpleErrorBoundary from "./components/SimpleErrorBoundary";
import {MockControl} from "./components/MockControl";

const queryClient = new QueryClient();

const App: React.FC = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <SimpleErrorBoundary>
        <BrowserRouter>
          <Routes>
            {navItems.map(({ to, page }) => (
              <Route key={to} path={to} element={page} />
            ))}
            {/* 404 页面 - 必须放在最后作为通配符路由 */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
        {/* Mock 控制面板 */}
        <MockControl />
      </SimpleErrorBoundary>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
