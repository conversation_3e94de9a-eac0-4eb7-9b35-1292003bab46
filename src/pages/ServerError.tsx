import {useNavigate} from 'react-router-dom';
import {But<PERSON>} from '@/components/ui/button';
import {Card, CardContent} from '@/components/ui/card';
import {AlertTriangle, ArrowLeft, Clock, Home, RefreshCw, ServerCrash, Wrench} from 'lucide-react';
import {ThemeProvider} from '../components/theme/ThemeProvider';

const ServerError = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleRetryLater = () => {
    // 可以设置一个延时重试
    setTimeout(() => {
      window.location.reload();
    }, 5000);
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-orange-500/5 flex items-center justify-center p-4">
        <div className="w-full max-w-2xl mx-auto">
          {/* 主要内容卡片 */}
          <Card className="border-0 shadow-2xl bg-card/80 backdrop-blur-sm">
            <CardContent className="p-8 sm:p-12 text-center">
              {/* 服务器错误图标动画 */}
              <div className="relative mb-8">
                <div className="text-8xl sm:text-9xl font-bold text-orange-500/20 select-none">
                  500
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="animate-pulse">
                    <ServerCrash className="h-16 w-16 sm:h-20 sm:w-20 text-orange-500" />
                  </div>
                </div>
              </div>

              {/* 标题和描述 */}
              <div className="mb-8 space-y-4">
                <h1 className="text-3xl sm:text-4xl font-bold text-foreground">
                  服务器错误
                </h1>
                <div className="flex items-center justify-center gap-2 text-muted-foreground">
                  <AlertTriangle className="h-4 w-4" />
                  <p className="text-lg">
                    服务器遇到了一个内部错误
                  </p>
                  <AlertTriangle className="h-4 w-4" />
                </div>
                <p className="text-sm text-muted-foreground max-w-md mx-auto">
                  服务器暂时无法处理您的请求。这可能是临时性问题，请稍后重试。如果问题持续存在，我们的技术团队已收到通知并正在处理。
                </p>
              </div>

              {/* 操作按钮组 */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <Button
                  onClick={handleRefresh}
                  size="lg"
                  className="w-full sm:w-auto min-w-[140px] h-12 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <RefreshCw className="mr-2 h-5 w-5" />
                  重新加载
                </Button>

                <Button
                  onClick={handleGoHome}
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto min-w-[140px] h-12 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <Home className="mr-2 h-5 w-5" />
                  返回首页
                </Button>

                <Button
                  onClick={handleGoBack}
                  variant="ghost"
                  size="lg"
                  className="w-full sm:w-auto min-w-[140px] h-12 text-base font-medium hover:bg-muted/50 transition-all duration-300"
                >
                  <ArrowLeft className="mr-2 h-5 w-5" />
                  返回上页
                </Button>
              </div>

              {/* 帮助信息 */}
              <div className="border-t border-border pt-6">
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-4">
                  <Wrench className="h-4 w-4" />
                  <span>您可以尝试：</span>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center justify-center gap-2 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer"
                       onClick={handleRefresh}>
                    <RefreshCw className="h-4 w-4 text-primary" />
                    <span>刷新页面</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer"
                       onClick={handleRetryLater}>
                    <Clock className="h-4 w-4 text-primary" />
                    <span>5秒后重试</span>
                  </div>
                </div>
              </div>

              {/* 错误代码信息 */}
              <div className="mt-6 p-4 bg-muted/20 rounded-lg">
                <p className="text-xs text-muted-foreground">
                  错误代码: HTTP 500 - Internal Server Error
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  时间: {new Date().toLocaleString('zh-CN')}
                </p>
              </div>

              {/* 装饰性元素 */}
              <div className="mt-8 flex justify-center">
                <div className="flex space-x-2">
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className="w-2 h-2 bg-orange-500/40 rounded-full animate-pulse"
                      style={{
                        animationDelay: `${i * 0.2}s`,
                        animationDuration: '1.5s'
                      }}
                    />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 底部提示 */}
          <div className="mt-6 text-center">
            <p className="text-xs text-muted-foreground">
              我们已收到错误报告，技术团队正在处理此问题
            </p>
          </div>
        </div>

        {/* 背景装饰 */}
        <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-orange-500/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/3 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
          <div className="absolute top-3/4 left-1/2 w-48 h-48 bg-orange-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        </div>
      </div>
    </ThemeProvider>
  );
};

export default ServerError;

