import React, {useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {Button} from '@/components/ui/button';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {ArrowLeft, Bug, MessageSquare, Settings as SettingsIcon, User} from 'lucide-react';
import {BasicInfoSettings} from '../components/settings/BasicInfoSettings';
import {ConversationSettings} from '../components/settings/ConversationSettings';
import {ThemeProvider} from '../components/theme/ThemeProvider';
import {useErrorHandler} from '../hooks/useErrorHandler';

const Settings = () => {
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('basicInfo');
  const { throwError } = useErrorHandler();

  const sections = [
    {
      id: 'basicInfo',
      label: '基本信息管理',
      icon: User,
      description: '管理个人信息和 UnionId'
    },
    {
      id: 'conversation',
      label: '对话管理',
      icon: MessageSquare,
      description: '对话样式和行为设置'
    },
    // 开发环境专用的错误测试
    ...((typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') ? [
      {
        id: 'errorTest',
        label: '错误测试',
        icon: Bug,
        description: '测试不同类型的错误处理'
      }
    ] : [])
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'basicInfo':
        return <BasicInfoSettings />;
      case 'conversation':
        return <ConversationSettings />;
      case 'errorTest':
        return <ErrorTestSettings throwError={throwError} />;
      default:
        return <BasicInfoSettings />;
    }
  };

  // 错误测试组件
  const ErrorTestSettings = ({ throwError }) => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">错误边界测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            以下按钮用于测试不同类型的错误处理机制。仅在开发环境可用。
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Button
              variant="destructive"
              onClick={() => throwError('这是一个测试错误')}
              className="w-full"
            >
              🚨 触发通用错误
            </Button>

            <Button
              variant="destructive"
              onClick={() => {
                throw new Error('JavaScript 运行时错误测试');
              }}
              className="w-full"
            >
              ⚡ JavaScript 错误
            </Button>

            <Button
              variant="destructive"
              onClick={() => {
                // 模拟异步错误
                setTimeout(() => {
                  throw new Error('异步错误测试');
                }, 100);
              }}
              className="w-full"
            >
              ⏰ 异步错误
            </Button>

            <Button
              variant="destructive"
              onClick={() => {
                // 模拟网络错误
                throwError('网络连接失败，请检查网络设置');
              }}
              className="w-full"
            >
              🌐 网络错误
            </Button>
          </div>

          <div className="mt-6 p-4 bg-muted/30 rounded-lg">
            <h4 className="font-semibold text-sm mb-2">测试说明：</h4>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• 通用错误：测试 ErrorBoundary 的基本错误捕获</li>
              <li>• JavaScript 错误：测试运行时错误处理</li>
              <li>• 异步错误：测试异步操作中的错误（可能不被 ErrorBoundary 捕获）</li>
              <li>• 网络错误：模拟网络请求失败的情况</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <ThemeProvider>
      <div className="h-screen bg-background text-foreground">
        <div className="h-full flex flex-col">
          {/* 顶部导航 */}
          <div className="
            h-12 sm:h-14 lg:h-16
            border-b border-border 
            bg-card/50 backdrop-blur-sm 
            flex items-center 
            px-2 sm:px-4 lg:px-6
            flex-shrink-0
          ">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="mr-2 sm:mr-4 h-8 w-8 sm:h-9 sm:w-9 p-0 sm:h-auto sm:w-auto sm:px-3"
            >
              <ArrowLeft className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">返回聊天</span>
            </Button>
            
            <div className="flex items-center gap-1 sm:gap-2">
              <SettingsIcon className="h-4 w-4 sm:h-5 sm:w-5" />
              <h1 className="text-sm sm:text-base lg:text-lg font-semibold">设置</h1>
            </div>
          </div>

          {/* 主内容区域 - 两列布局 */}
          <div className="flex-1 overflow-hidden">
            <div className="h-full flex">
              {/* 左侧导航 */}
              <div className="
                w-64 lg:w-80
                border-r border-border
                bg-card/30
                flex-shrink-0
                hidden md:block
              ">
                <div className="p-4 space-y-2">
                  <h2 className="text-sm font-medium text-muted-foreground mb-4">设置分类</h2>
                  {sections.map((section) => (
                    <Button
                      key={section.id}
                      variant={activeSection === section.id ? 'default' : 'ghost'}
                      onClick={() => setActiveSection(section.id)}
                      className="w-full justify-start h-auto p-3"
                    >
                      <div className="flex items-start gap-3">
                        <section.icon className="h-5 w-5 mt-0.5 flex-shrink-0" />
                        <div className="text-left">
                          <div className="font-medium">{section.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {section.description}
                          </div>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              {/* 移动端导航 */}
              <div className="md:hidden w-full">
                <div className="p-4 border-b border-border">
                  <div className="flex gap-2 overflow-x-auto">
                    {sections.map((section) => (
                      <Button
                        key={section.id}
                        variant={activeSection === section.id ? 'default' : 'outline'}
                        onClick={() => setActiveSection(section.id)}
                        className="flex items-center gap-2 whitespace-nowrap"
                        size="sm"
                      >
                        <section.icon className="h-4 w-4" />
                        {section.label}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* 右侧内容区域 */}
              <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-auto p-4 lg:p-6">
                  <div className="max-w-4xl mx-auto">
                    {/* 当前选中的标题 */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-2">
                        {sections.find(s => s.id === activeSection)?.icon && (
                          React.createElement(sections.find(s => s.id === activeSection).icon, {
                            className: "h-6 w-6 text-primary"
                          })
                        )}
                        <h2 className="text-xl font-semibold">
                          {sections.find(s => s.id === activeSection)?.label}
                        </h2>
                      </div>
                      <p className="text-muted-foreground">
                        {sections.find(s => s.id === activeSection)?.description}
                      </p>
                    </div>

                    {/* 内容区域 */}
                    {renderContent()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ThemeProvider>
  );
};

export default Settings;
