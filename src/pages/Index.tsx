import React, {useEffect} from 'react';
import {useChatStore} from '../store/chatStore';
import {ChatLayout} from '../components/chat/ChatLayout';
import {ThemeProvider} from '../components/theme/ThemeProvider';

const Index = () => {
  const { loadConversations } = useChatStore();

  useEffect(() => {
    // 初始化加载会话数据
    loadConversations()
      .catch((error) => {
        console.error('加载会话失败:', error);
      });
  }, []); // 只在组件挂载时执行一次

  return (
    <ThemeProvider>
      <div className="h-screen bg-background text-foreground">
        <ChatLayout />
      </div>
    </ThemeProvider>
  );
};

export default Index;
