import React from 'react';
import {useNavigate} from 'react-router-dom';
import {But<PERSON>} from '@/components/ui/button';
import {Card, CardContent} from '@/components/ui/card';
import {AlertCircle, ArrowLeft, Home, RefreshCw, Search, Sparkles} from 'lucide-react';
import {ThemeProvider} from '../components/theme/ThemeProvider';

const NotFound = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center p-4">
        <div className="w-full max-w-2xl mx-auto">
          {/* 主要内容卡片 */}
          <Card className="border-0 shadow-2xl bg-card/80 backdrop-blur-sm">
            <CardContent className="p-8 sm:p-12 text-center">
              {/* 404 数字动画 */}
              <div className="relative mb-8">
                <div className="text-8xl sm:text-9xl font-bold text-primary/20 select-none">
                  404
                </div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="animate-bounce">
                    <AlertCircle className="h-16 w-16 sm:h-20 sm:w-20 text-primary" />
                  </div>
                </div>
              </div>

              {/* 标题和描述 */}
              <div className="mb-8 space-y-4">
                <h1 className="text-3xl sm:text-4xl font-bold text-foreground">
                  页面未找到
                </h1>
                <div className="flex items-center justify-center gap-2 text-muted-foreground">
                  <Sparkles className="h-4 w-4" />
                  <p className="text-lg">
                    抱歉，您访问的页面不存在或已被移动
                  </p>
                  <Sparkles className="h-4 w-4" />
                </div>
                <p className="text-sm text-muted-foreground max-w-md mx-auto">
                  您访问的页面地址不存在，可能是 URL 输入错误或页面已被删除。请检查地址拼写或使用下方按钮导航到其他页面。
                </p>
              </div>

              {/* 操作按钮组 */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <Button
                  onClick={handleGoHome}
                  size="lg"
                  className="w-full sm:w-auto min-w-[140px] h-12 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <Home className="mr-2 h-5 w-5" />
                  返回首页
                </Button>

                <Button
                  onClick={handleGoBack}
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto min-w-[140px] h-12 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <ArrowLeft className="mr-2 h-5 w-5" />
                  返回上页
                </Button>

                <Button
                  onClick={handleRefresh}
                  variant="ghost"
                  size="lg"
                  className="w-full sm:w-auto min-w-[140px] h-12 text-base font-medium hover:bg-muted/50 transition-all duration-300"
                >
                  <RefreshCw className="mr-2 h-5 w-5" />
                  刷新页面
                </Button>
              </div>

              {/* 搜索建议 */}
              <div className="border-t border-border pt-6">
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-4">
                  <Search className="h-4 w-4" />
                  <span>或者尝试以下操作：</span>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center justify-center gap-2 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer"
                       onClick={handleGoHome}>
                    <Home className="h-4 w-4 text-primary" />
                    <span>访问主页</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer"
                       onClick={() => navigate('/settings')}>
                    <AlertCircle className="h-4 w-4 text-primary" />
                    <span>查看设置</span>
                  </div>
                </div>
              </div>

              {/* 装饰性元素 */}
              <div className="mt-8 flex justify-center">
                <div className="flex space-x-2">
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className="w-2 h-2 bg-primary/40 rounded-full animate-pulse"
                      style={{
                        animationDelay: `${i * 0.2}s`,
                        animationDuration: '1.5s'
                      }}
                    />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 底部提示 */}
          <div className="mt-6 text-center">
            <p className="text-xs text-muted-foreground">
              如果问题持续存在，请联系技术支持
            </p>
          </div>
        </div>

        {/* 背景装饰 */}
        <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
          <div className="absolute top-3/4 left-1/2 w-48 h-48 bg-accent/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        </div>
      </div>
    </ThemeProvider>
  );
};

export default NotFound;

