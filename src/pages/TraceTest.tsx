import React from 'react';
import {TraceTreeCompact} from '../components/trace/TraceTreeCompact';
import {TraceTreeAlignmentTest} from '../components/trace/TraceTreeAlignmentTest';
import {ThemeProvider} from '../components/theme/ThemeProvider';

const TraceTest = () => {
  return (
    <ThemeProvider>
      <div className="min-h-screen bg-background text-foreground p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          <h1 className="text-2xl font-bold">链路追踪组件测试</h1>

          {/* 简化版组件 - 使用验证过的连线逻辑 */}
          <TraceTreeCompact />

          {/* 连线对齐测试组件 */}
          <TraceTreeAlignmentTest />
        </div>
      </div>
    </ThemeProvider>
  );
};

export default TraceTest;

