import {useNavigate} from 'react-router-dom';

export const useErrorHandler = () => {
  const navigate = useNavigate();

  // 处理 API 响应错误
  const handleApiError = (response, customMessage = null) => {
    const status = response?.status;

    if (status === 401) {
      // 未认证 - 跳转到登录或无权限页面
      navigate('/unauthorized');
      return;
    }

    if (status === 403) {
      // 无权限 - 跳转到无权限页面
      navigate('/unauthorized');
      return;
    }

    if (status === 404) {
      // 资源未找到 - 跳转到 404 页面
      navigate('/404');
      return;
    }

    if (status >= 500) {
      // 服务器错误 - 在开发环境跳转到错误页面，生产环境抛出错误让 ErrorBoundary 处理
      if (process.env.NODE_ENV === 'development') {
        navigate('/server-error');
      } else {
        throw new Error(customMessage || `服务器错误 (${status}): ${response?.statusText || '未知错误'}`);
      }
      return;
    }

    // 其他错误 - 抛出错误让 ErrorBoundary 处理
    throw new Error(customMessage || `请求失败 (${status}): ${response?.statusText || '未知错误'}`);
  };

  // 手动抛出错误（用于测试或特殊情况）
  const throwError = (message, type = 'generic') => {
    const error = new Error(message);
    error.type = type;
    throw error;
  };

  // 处理网络错误
  const handleNetworkError = (error) => {
    if (!navigator.onLine) {
      throw new Error('网络连接已断开，请检查您的网络连接');
    }

    if (error.name === 'AbortError') {
      throw new Error('请求已取消');
    }

    if (error.name === 'TimeoutError') {
      throw new Error('请求超时，请稍后重试');
    }

    throw new Error(`网络错误: ${error.message}`);
  };

  // 安全的异步操作包装器
  const safeAsync = async (asyncFn, errorMessage = '操作失败') => {
    try {
      return await asyncFn();
    } catch (error) {
      if (error.response) {
        // API 响应错误
        handleApiError(error.response, errorMessage);
      } else if (error.request) {
        // 网络错误
        handleNetworkError(error);
      } else {
        // 其他错误
        throw new Error(`${errorMessage}: ${error.message}`);
      }
    }
  };

  // 权限检查
  const checkPermission = (requiredPermission, userPermissions = []) => {
    if (!userPermissions.includes(requiredPermission)) {
      navigate('/unauthorized');
      return false;
    }
    return true;
  };

  // 登录状态检查
  const requireAuth = (redirectTo = '/unauthorized') => {
    const token = localStorage.getItem('token');
    const isAuthenticated = !!token; // 这里可以添加更复杂的验证逻辑

    if (!isAuthenticated) {
      navigate(redirectTo);
      return false;
    }
    return true;
  };

  return {
    handleApiError,
    handleNetworkError,
    throwError,
    safeAsync,
    checkPermission,
    requireAuth
  };
};

