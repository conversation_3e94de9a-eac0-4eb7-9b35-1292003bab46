declare module "*.css" {
  const content: Record<string, string>;
  export default content;
}

declare module "*.scss" {
  const content: Record<string, string>;
  export default content;
}

declare module "*.module.css" {
  const classes: Record<string, string>;
  export default classes;
}

declare module "*.module.scss" {
  const classes: Record<string, string>;
  export default classes;
}

// 扩展 Window 接口
declare global {
  interface Window {
    // 添加任何全局变量的类型定义
    // 如果需要添加全局变量，在这里定义
    [key: string]: unknown;
  }
}

import * as React from 'react';

// React 组件 props 的通用类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// 导航项类型
export interface NavItem {
  title: string;
  to: string;
  icon: React.ReactElement;
  page: React.ReactElement;
  hidden?: boolean;
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export {};

