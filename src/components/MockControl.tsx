import React, {useEffect, useState} from 'react';
import {Switch} from '@/components/ui/switch';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Separator} from '@/components/ui/separator';
import {Badge} from '@/components/ui/badge';
import {AlertTriangle, Database, Settings, Zap} from 'lucide-react';
import {getMockConfig, MockConfig, saveMockConfig} from '@/mock';

export const MockControl: React.FC = () => {
    const [config, setConfig] = useState<MockConfig>(() => getMockConfig());
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            // Ctrl/Cmd + Shift + M 打开 Mock 控制面板
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'M') {
                e.preventDefault();
                setIsOpen(!isOpen);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [isOpen]);

    const handleConfigChange = (updates: Partial<MockConfig>) => {
        const newConfig = {...config, ...updates};
        setConfig(newConfig);
        saveMockConfig(updates);
    };

    const resetConfig = () => {
        localStorage.removeItem('mockConfig');
        const defaultConfig = getMockConfig();
        setConfig(defaultConfig);
    };

    const clearMockData = () => {
        localStorage.removeItem('mock_conversations');
        localStorage.removeItem('mock_shares');
        localStorage.removeItem('mock_userConfig');
        alert('Mock 数据已清除');
    };

    if (!isOpen) {
        return (
            <div className="fixed bottom-4 right-4 z-50">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsOpen(true)}
                    className="shadow-lg"
                >
                    <Settings className="w-4 h-4 mr-2"/>
                    Mock
                    {config.enabled && (
                        <Badge variant="secondary" className="ml-2">
                            ON
                        </Badge>
                    )}
                </Button>
            </div>
        );
    }

    return (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle className="flex items-center gap-2">
                                <Database className="w-5 h-5"/>
                                Mock 控制面板
                            </CardTitle>
                            <CardDescription>
                                管理 API Mock 配置和数据
                            </CardDescription>
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsOpen(false)}
                        >
                            ✕
                        </Button>
                    </div>
                </CardHeader>

                <CardContent className="space-y-6">
                    {/* 主开关 */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-1">
                            <Label className="text-base font-medium">启用 Mock</Label>
                            <p className="text-sm text-muted-foreground">
                                开启后将使用模拟数据，关闭后调用真实 API
                            </p>
                        </div>
                        <Switch
                            checked={config.enabled}
                            onCheckedChange={(enabled) => handleConfigChange({enabled})}
                        />
                    </div>

                    <Separator/>

                    {/* 延迟配置 */}
                    <div className="space-y-4">
                        <div className="flex items-center gap-2">
                            <Zap className="w-4 h-4"/>
                            <Label className="text-base font-medium">响应延迟 (毫秒)</Label>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="min-delay">最小延迟</Label>
                                <Input
                                    id="min-delay"
                                    type="number"
                                    value={config.delay.min}
                                    onChange={(e) => handleConfigChange({
                                        delay: {...config.delay, min: Number(e.target.value)}
                                    })}
                                    min="0"
                                    max="5000"
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="max-delay">最大延迟</Label>
                                <Input
                                    id="max-delay"
                                    type="number"
                                    value={config.delay.max}
                                    onChange={(e) => handleConfigChange({
                                        delay: {...config.delay, max: Number(e.target.value)}
                                    })}
                                    min="0"
                                    max="5000"
                                />
                            </div>
                        </div>
                    </div>

                    <Separator/>

                    {/* 错误率配置 */}
                    <div className="space-y-4">
                        <div className="flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4"/>
                            <Label className="text-base font-medium">错误模拟</Label>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="error-rate">
                                错误率: {(config.errorRate * 100).toFixed(1)}%
                            </Label>
                            <Input
                                id="error-rate"
                                type="range"
                                min="0"
                                max="0.5"
                                step="0.01"
                                value={config.errorRate}
                                onChange={(e) => handleConfigChange({
                                    errorRate: Number(e.target.value)
                                })}
                                className="w-full"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="stream-delay">流式响应延迟 (毫秒)</Label>
                            <Input
                                id="stream-delay"
                                type="number"
                                value={config.streamDelay}
                                onChange={(e) => handleConfigChange({
                                    streamDelay: Number(e.target.value)
                                })}
                                min="10"
                                max="200"
                            />
                        </div>
                    </div>

                    <Separator/>

                    {/* 操作按钮 */}
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            onClick={resetConfig}
                            className="flex-1"
                        >
                            重置配置
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={clearMockData}
                            className="flex-1"
                        >
                            清除数据
                        </Button>
                    </div>

                    {/* 状态信息 */}
                    <div className="bg-muted p-4 rounded-lg space-y-2">
                        <div className="text-sm font-medium">当前状态</div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>Mock 状态: {config.enabled ? '✅ 已启用' : '❌ 已禁用'}</div>
                            <div>延迟范围: {config.delay.min}-{config.delay.max}ms</div>
                            <div>错误率: {(config.errorRate * 100).toFixed(1)}%</div>
                            <div>流式延迟: {config.streamDelay}ms</div>
                        </div>
                    </div>

                    {/* 快捷键提示 */}
                    <div className="text-xs text-muted-foreground text-center">
                        快捷键: Ctrl/Cmd + Shift + M
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

