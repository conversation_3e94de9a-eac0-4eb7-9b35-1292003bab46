import React, {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {Input} from '@/components/ui/input';
import {Separator} from '@/components/ui/separator';
import {ScrollArea} from '@/components/ui/scroll-area';
import {Badge} from '@/components/ui/badge';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Textarea} from '@/components/ui/textarea';
import {Label} from '@/components/ui/label';
import {Button} from '@/components/ui/button';
import {
    ArrowLeft,
    Check,
    Copy,
    Download,
    Edit,
    Plus,
    Save,
    Search,
    Settings,
    Terminal,
    Trash2,
    User,
    Users,
    X
} from 'lucide-react';
import {UnionIdItem, UserConfig} from '@/service/types';

type EditingState = string | null;
type CopiedState = string | null;

// 统计信息类型
interface StatsData {
    total: number;
    active: number;
    filtered: number;
    withDescription: number;
}

export const ConfigManager: React.FC = () => {
    const navigate = useNavigate();
    const [unionIds, setUnionIds] = useState<UnionIdItem[]>([]);
    const [newUnionId, setNewUnionId] = useState<string>('');
    const [editingId, setEditingId] = useState<EditingState>(null);
    const [editValue, setEditValue] = useState<string>('');
    const [copiedId, setCopiedId] = useState<CopiedState>(null);
    const [commandTemplate, setCommandTemplate] = useState<string>('');
    const [searchQuery, setSearchQuery] = useState<string>('');

    // 从 localStorage 加载 unionIds
    useEffect(() => {
        const userConfig: UserConfig = JSON.parse(localStorage.getItem('userConfig') || '{}');
        if (userConfig.unionIds) {
            // 确保日期对象正确转换
            const processedUnionIds: UnionIdItem[] = userConfig.unionIds.map(item => ({
                ...item,
                createdAt: new Date(item.createdAt),
                updatedAt: new Date(item.updatedAt)
            }));
            setUnionIds(processedUnionIds);
        }
    }, []);

    // 保存 unionIds 到 localStorage
    const saveUnionIds = async (newUnionIds: UnionIdItem[]): Promise<void> => {
        const userConfig: UserConfig = JSON.parse(localStorage.getItem('userConfig') || '{}');
        userConfig.unionIds = newUnionIds;
        localStorage.setItem('userConfig', JSON.stringify(userConfig));
        setUnionIds(newUnionIds);
    };

    // 过滤 UnionId 列表
    const filteredUnionIds: UnionIdItem[] = unionIds.filter((item: UnionIdItem) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.value.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    // 计算统计信息
    const statsData: StatsData = {
        total: unionIds.length,
        active: unionIds.filter((item: UnionIdItem) => item.isActive !== false).length,
        filtered: filteredUnionIds.length,
        withDescription: unionIds.filter((item: UnionIdItem) => item.description && item.description.trim()).length
    };

    // 添加新的 UnionId
    const handleAddUnionId = (): void => {
        if (!newUnionId.trim()) return;

        const newId: UnionIdItem = {
            id: Date.now().toString(),
            value: newUnionId.trim(),
            name: `用户 ${unionIds.length + 1}`,
            createdAt: new Date(),
            updatedAt: new Date(),
            description: '',
            tags: [],
            isActive: true
        };

        const updatedIds: UnionIdItem[] = [...unionIds, newId];
        saveUnionIds(updatedIds);
        setNewUnionId('');
    };

    // 删除 UnionId
    const handleDeleteUnionId = (id: string): void => {
        const updatedIds: UnionIdItem[] = unionIds.filter((item: UnionIdItem) => item.id !== id);
        saveUnionIds(updatedIds);
    };

    // 开始编辑
    const handleStartEdit = (item: UnionIdItem): void => {
        setEditingId(item.id);
        setEditValue(item.value);
    };

    // 保存编辑
    const handleSaveEdit = (): void => {
        if (!editValue.trim()) return;

        const updatedIds: UnionIdItem[] = unionIds.map((item: UnionIdItem) =>
            item.id === editingId
                ? {...item, value: editValue.trim(), updatedAt: new Date()}
                : item
        );

        saveUnionIds(updatedIds);
        setEditingId(null);
        setEditValue('');
    };

    // 取消编辑
    const handleCancelEdit = (): void => {
        setEditingId(null);
        setEditValue('');
    };

    // 复制到剪贴板
    const handleCopy = async (value: string): Promise<void> => {
        try {
            await navigator.clipboard.writeText(value);
            setCopiedId(value);
            setTimeout(() => setCopiedId(null), 2000);
        } catch (error) {
            console.error('复制失败:', error);
        }
    };

    // 更新名称和描述
    const handleUpdateMeta = (id: string, field: keyof Pick<UnionIdItem, 'name' | 'description'>, value: string): void => {
        const updatedIds: UnionIdItem[] = unionIds.map((item: UnionIdItem) =>
            item.id === id
                ? {...item, [field]: value, updatedAt: new Date()}
                : item
        );
        saveUnionIds(updatedIds);
    };

    // 生成命令模板
    const generateCommandTemplate = (): void => {
        if (unionIds.length === 0) {
            setCommandTemplate('暂无可用的 UnionId');
            return;
        }

        const template: string = unionIds.map((item: UnionIdItem) =>
            `/UnionId ${item.value} # ${item.name}${item.description ? ' - ' + item.description : ''}`
        ).join('\n');

        setCommandTemplate(template);
    };

    // 导出 UnionId 数据
    const handleExport = (): void => {
        const csvContent: string = unionIds.map((item: UnionIdItem) =>
            `${item.value},${item.name},${item.description || ''}`
        ).join('\n');

        const blob: Blob = new Blob([csvContent], {type: 'text/csv'});
        const url: string = URL.createObjectURL(blob);
        const a: HTMLAnchorElement = document.createElement('a');
        a.href = url;
        a.download = `unionids_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
    };

    // 处理键盘事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>): void => {
        if (e.key === 'Enter') {
            handleAddUnionId();
        }
    };

    // 处理输入变化
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
        setNewUnionId(e.target.value);
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
        setSearchQuery(e.target.value);
    };

    const handleEditValueChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
        setEditValue(e.target.value);
    };

    const handleNameChange = (id: string) => (e: React.ChangeEvent<HTMLInputElement>): void => {
        handleUpdateMeta(id, 'name', e.target.value);
    };

    const handleDescriptionChange = (id: string) => (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
        handleUpdateMeta(id, 'description', e.target.value);
    };

    return (
        <div className="h-full flex flex-col">
            {/* 顶部导航 - 响应式高度和间距 */}
            <div className="
        h-12 sm:h-14 lg:h-16
        border-b border-border 
        bg-card/50 backdrop-blur-sm 
        flex items-center 
        px-2 sm:px-4 lg:px-6
        flex-shrink-0
      ">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate('/')}
                    className="mr-2 sm:mr-4 h-8 w-8 sm:h-9 sm:w-9 p-0 sm:h-auto sm:w-auto sm:px-3"
                >
                    <ArrowLeft className="h-4 w-4 sm:mr-2"/>
                    <span className="hidden sm:inline">返回聊天</span>
                </Button>

                <div className="flex items-center gap-1 sm:gap-2">
                    <Settings className="h-4 w-4 sm:h-5 sm:w-5"/>
                    <h1 className="text-sm sm:text-base lg:text-lg font-semibold">配置管理</h1>
                </div>
            </div>

            {/* 主内容区域 - 响应式布局 */}
            <div className="flex-1 overflow-hidden p-2 sm:p-4 lg:p-6">
                <div className="
          max-w-full sm:max-w-2xl lg:max-w-4xl xl:max-w-6xl 
          mx-auto 
          space-y-3 sm:space-y-4 lg:space-y-6
          h-full
        ">
                    {/* UnionId 管理 - 增强版 */}
                    <Card className="flex-1 overflow-hidden">
                        <CardHeader className="pb-2 sm:pb-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="flex items-center gap-2 text-sm sm:text-base lg:text-lg">
                                        <Users className="h-4 w-4 sm:h-5 sm:w-5 text-primary"/>
                                        UnionId 管理
                                        <Badge variant="secondary" className="ml-2">
                                            {unionIds.length} 个
                                        </Badge>
                                    </CardTitle>
                                    <p className="text-xs sm:text-sm text-muted-foreground mt-1">
                                        管理个人 UnionId，在指令输入中可以快速选择使用
                                    </p>
                                </div>

                                {/* 工具按钮 */}
                                <div className="flex items-center gap-2">
                                    {unionIds.length > 0 && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={handleExport}
                                            className="hidden sm:flex"
                                        >
                                            <Download className="h-4 w-4 mr-1"/>
                                            导出
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-3 sm:space-y-4 h-full overflow-hidden">
                            {/* 添加新 UnionId - 响应式布局 */}
                            <div className="space-y-2">
                                <div className="flex flex-col sm:flex-row gap-2">
                                    <Input
                                        placeholder="输入新的 UnionId..."
                                        value={newUnionId}
                                        onChange={handleInputChange}
                                        onKeyDown={handleKeyDown}
                                        className="flex-1 text-sm"
                                    />
                                    <Button
                                        onClick={handleAddUnionId}
                                        disabled={!newUnionId.trim()}
                                        className="w-full sm:w-auto"
                                    >
                                        <Plus className="h-4 w-4 mr-1 sm:mr-2"/>
                                        <span className="text-sm">添加 UnionId</span>
                                    </Button>
                                </div>

                                {/* 搜索框 */}
                                {unionIds.length > 3 && (
                                    <div className="relative">
                                        <Search
                                            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"/>
                                        <Input
                                            placeholder="搜索 UnionId..."
                                            value={searchQuery}
                                            onChange={handleSearchChange}
                                            className="pl-10 text-sm"
                                        />
                                    </div>
                                )}
                            </div>

                            <Separator/>

                            {/* 统计信息 */}
                            {unionIds.length > 0 && (
                                <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4">
                                    <Card className="p-2 sm:p-3">
                                        <div className="text-center">
                                            <div
                                                className="text-lg sm:text-xl font-bold text-primary">{statsData.total}</div>
                                            <div className="text-xs text-muted-foreground">总数量</div>
                                        </div>
                                    </Card>
                                    <Card className="p-2 sm:p-3">
                                        <div className="text-center">
                                            <div className="text-lg sm:text-xl font-bold text-green-600">
                                                {statsData.active}
                                            </div>
                                            <div className="text-xs text-muted-foreground">活跃用户</div>
                                        </div>
                                    </Card>
                                    <Card className="p-2 sm:p-3">
                                        <div className="text-center">
                                            <div
                                                className="text-lg sm:text-xl font-bold text-blue-600">{statsData.filtered}</div>
                                            <div className="text-xs text-muted-foreground">搜索结果</div>
                                        </div>
                                    </Card>
                                    <Card className="p-2 sm:p-3">
                                        <div className="text-center">
                                            <div className="text-lg sm:text-xl font-bold text-orange-600">
                                                {statsData.withDescription}
                                            </div>
                                            <div className="text-xs text-muted-foreground">有描述</div>
                                        </div>
                                    </Card>
                                </div>
                            )}

                            {/* UnionId 列表 - 响应式滚动区域 */}
                            <div className="flex-1 overflow-hidden">
                                {filteredUnionIds.length === 0 ? (
                                    <div className="text-center py-6 sm:py-8 text-muted-foreground">
                                        <Users className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 opacity-50"/>
                                        {searchQuery ? (
                                            <>
                                                <p className="text-sm sm:text-base">未找到匹配的 UnionId</p>
                                                <p className="text-xs sm:text-sm">尝试调整搜索条件</p>
                                            </>
                                        ) : (
                                            <>
                                                <p className="text-sm sm:text-base">暂无 UnionId</p>
                                                <p className="text-xs sm:text-sm">添加 UnionId 以便在聊天中快速使用</p>
                                            </>
                                        )}
                                    </div>
                                ) : (
                                    <ScrollArea className="h-full max-h-[50vh] sm:max-h-[60vh] lg:max-h-[70vh]">
                                        <div className="space-y-2 sm:space-y-3 pr-2">
                                            {filteredUnionIds.map((item: UnionIdItem) => (
                                                <Card key={item.id}
                                                      className="p-2 sm:p-3 lg:p-4 hover:shadow-md transition-shadow">
                                                    <div className="space-y-2 sm:space-y-3">
                                                        {/* 第一行：名称和操作按钮 - 响应式布局 */}
                                                        <div
                                                            className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                                            <div className="flex items-center gap-2 flex-1 min-w-0">
                                                                <Input
                                                                    value={item.name}
                                                                    onChange={handleNameChange(item.id)}
                                                                    className="font-medium w-full sm:w-40 text-sm"
                                                                    placeholder="名称"
                                                                />
                                                                <Badge variant="outline"
                                                                       className="text-xs flex-shrink-0">
                                                                    {item.createdAt.toLocaleDateString()}
                                                                </Badge>
                                                                {item.isActive !== false && (
                                                                    <Badge variant="default"
                                                                           className="text-xs flex-shrink-0">
                                                                        活跃
                                                                    </Badge>
                                                                )}
                                                            </div>

                                                            <div className="flex gap-1 justify-end">
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => handleCopy(item.value)}
                                                                    className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                                                                    title="复制 UnionId"
                                                                >
                                                                    {copiedId === item.value ? (
                                                                        <Check
                                                                            className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-green-600"/>
                                                                    ) : (
                                                                        <Copy className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                                    )}
                                                                </Button>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => handleStartEdit(item)}
                                                                    className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                                                                    title="编辑 UnionId"
                                                                >
                                                                    <Edit className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                                </Button>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => handleDeleteUnionId(item.id)}
                                                                    className="h-6 w-6 sm:h-8 sm:w-8 p-0 text-destructive hover:text-destructive"
                                                                    title="删除 UnionId"
                                                                >
                                                                    <Trash2 className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                                </Button>
                                                            </div>
                                                        </div>

                                                        {/* 第二行：UnionId 值 */}
                                                        <div>
                                                            <Label className="text-xs text-muted-foreground">UnionId
                                                                值</Label>
                                                            {editingId === item.id ? (
                                                                <div className="flex gap-2 mt-1">
                                                                    <Input
                                                                        value={editValue}
                                                                        onChange={handleEditValueChange}
                                                                        className="flex-1 font-mono text-xs sm:text-sm"
                                                                        placeholder="输入 UnionId"
                                                                    />
                                                                    <Button
                                                                        size="sm"
                                                                        onClick={handleSaveEdit}
                                                                        className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                                                                        disabled={!editValue.trim()}
                                                                    >
                                                                        <Save className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                                    </Button>
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={handleCancelEdit}
                                                                        className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                                                                    >
                                                                        <X className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                                    </Button>
                                                                </div>
                                                            ) : (
                                                                <div className="
                                  mt-1 p-2 
                                  bg-muted/50 rounded 
                                  font-mono 
                                  text-xs sm:text-sm 
                                  break-all
                                  border
                                  hover:bg-muted/70
                                  cursor-pointer
                                  transition-colors
                                "
                                                                     onClick={() => handleCopy(item.value)}
                                                                     title="点击复制"
                                                                >
                                                                    {item.value}
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* 第三行：描述 */}
                                                        <div>
                                                            <Label
                                                                className="text-xs text-muted-foreground">描述信息</Label>
                                                            <Textarea
                                                                value={item.description || ''}
                                                                onChange={handleDescriptionChange(item.id)}
                                                                placeholder="添加描述信息，便于识别和管理..."
                                                                className="mt-1 min-h-[50px] sm:min-h-[60px] text-xs sm:text-sm"
                                                            />
                                                        </div>

                                                        {/* 更新时间 */}
                                                        {item.updatedAt && (
                                                            <div className="text-xs text-muted-foreground">
                                                                最后更新：{item.updatedAt.toLocaleString()}
                                                            </div>
                                                        )}
                                                    </div>
                                                </Card>
                                            ))}
                                        </div>
                                    </ScrollArea>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* 命令模板生成 - 响应式显示 */}
                    {unionIds.length > 0 && (
                        <Card>
                            <CardHeader className="pb-2 sm:pb-4">
                                <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
                                    <Terminal className="h-4 w-4 sm:h-5 sm:w-5"/>
                                    命令模板生成器
                                </CardTitle>
                                <p className="text-xs sm:text-sm text-muted-foreground">
                                    生成可直接在聊天中使用的批量命令模板
                                </p>
                            </CardHeader>
                            <CardContent className="space-y-3 sm:space-y-4">
                                <Button onClick={generateCommandTemplate} className="w-full">
                                    <Terminal className="h-4 w-4 mr-2"/>
                                    生成所有 UnionId 的命令模板
                                </Button>

                                {commandTemplate && (
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <Label className="text-xs sm:text-sm font-medium">生成的命令模板</Label>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleCopy(commandTemplate)}
                                                className="h-6 sm:h-8 px-2"
                                            >
                                                {copiedId === commandTemplate ? (
                                                    <Check className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-green-600 mr-1"/>
                                                ) : (
                                                    <Copy className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-1"/>
                                                )}
                                                <span className="text-xs sm:text-sm">复制全部</span>
                                            </Button>
                                        </div>
                                        <Textarea
                                            value={commandTemplate}
                                            readOnly
                                            className="
                        font-mono 
                        text-xs sm:text-sm 
                        min-h-[80px] sm:min-h-[120px] 
                        bg-muted/50
                        border-dashed
                      "
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            💡 复制后可直接粘贴到聊天输入框中使用，系统会自动识别每个 UnionId 命令
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    )}

                    {/* 使用说明 - 响应式显示 */}
                    <Card className="hidden lg:block">
                        <CardHeader>
                            <CardTitle className="text-base flex items-center gap-2">
                                <User className="h-4 w-4"/>
                                UnionId 使用指南
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4 text-sm text-muted-foreground">
                            <div className="grid md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <h4 className="font-medium text-foreground">管理功能</h4>
                                    <ul className="space-y-1 ml-4">
                                        <li>• 添加常用的 UnionId 以便快速使用</li>
                                        <li>• 为每个 UnionId 设置易识别的名称和描述</li>
                                        <li>• 支持编辑、复制和删除操作</li>
                                        <li>• 支持搜索和批量导出功能</li>
                                    </ul>
                                </div>

                                <div className="space-y-2">
                                    <h4 className="font-medium text-foreground">在聊天中使用</h4>
                                    <ul className="space-y-1 ml-4">
                                        <li>• 在聊天输入框中输入 "/" 可快速选择 UnionId</li>
                                        <li>• 使用命令模板可以批量添加多个 UnionId</li>
                                        <li>• 点击 UnionId 值可快速复制到剪贴板</li>
                                        <li>• 支持拖拽调整命令顺序</li>
                                    </ul>
                                </div>
                            </div>

                            <div
                                className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">💡 小贴士</h4>
                                <p className="text-blue-700 dark:text-blue-300 text-xs">
                                    建议为每个 UnionId 添加有意义的名称和描述，这样在聊天中选择时更容易识别。
                                    描述可以包含用户角色、测试环境等信息。
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
};
