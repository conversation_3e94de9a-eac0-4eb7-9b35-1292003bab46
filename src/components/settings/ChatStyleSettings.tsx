import React, {useEffect, useState} from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Label} from '@/components/ui/label';
import {Badge} from '@/components/ui/badge';
import {Bot, CheckCircle, MessageSquare, Palette, RefreshCw, Save, User} from 'lucide-react';
import {useChatStore} from '../../store/chatStore';

export const ChatStyleSettings = () => {
  const { userConfig, updateUserConfig } = useChatStore();
  const [chatStyle, setChatStyle] = useState('bubble'); // 'bubble' 或 'flat'
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);

  // 从用户配置中加载聊天样式设置
  useEffect(() => {
    if (userConfig.chatStyle) {
      setChatStyle(userConfig.chatStyle);
    }
  }, [userConfig]);

  // 保存设置
  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus(null);
    
    try {
      await updateUserConfig({ chatStyle });
      setSaveStatus('success');
      setTimeout(() => setSaveStatus(null), 3000);
    } catch (error) {
      console.error('保存聊天样式设置失败:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus(null), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // 重置为默认设置
  const handleReset = () => {
    setChatStyle('bubble');
  };

  const getSaveStatusIcon = () => {
    switch (saveStatus) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* 样式选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            对话样式设置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 样式选项 */}
          <div className="space-y-4">
            <Label className="text-base font-medium">选择对话样式</Label>
            
            {/* 气泡样式 */}
            <div 
              className={`
                p-4 border-2 rounded-lg cursor-pointer transition-all
                ${chatStyle === 'bubble' 
                  ? 'border-primary bg-primary/5' 
                  : 'border-border hover:border-primary/50'
                }
              `}
              onClick={() => setChatStyle('bubble')}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <span className="font-medium">气泡样式</span>
                  {chatStyle === 'bubble' && (
                    <Badge variant="default" className="text-xs">当前</Badge>
                  )}
                </div>
                <div className="w-4 h-4 rounded-full border-2 border-primary flex items-center justify-center">
                  {chatStyle === 'bubble' && (
                    <div className="w-2 h-2 rounded-full bg-primary" />
                  )}
                </div>
              </div>
              
              {/* 气泡样式预览 - 不显示用户头像 */}
              <div className="space-y-3 bg-muted/30 p-3 rounded">
                {/* 用户消息 - 右对齐，无头像 */}
                <div className="flex justify-end">
                  <div className="max-w-[70%]">
                    <div className="bg-primary text-primary-foreground px-3 py-2 rounded-2xl rounded-br-md">
                      <div className="text-xs">用户消息示例</div>
                    </div>
                  </div>
                </div>
                
                {/* 助手消息 - 左对齐，有头像 */}
                <div className="flex justify-start">
                  <div className="flex items-start gap-2 max-w-[70%]">
                    <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center flex-shrink-0">
                      <Bot className="h-3 w-3" />
                    </div>
                    <div className="bg-card border px-3 py-2 rounded-2xl rounded-bl-md">
                      <div className="text-xs">助手回复示例</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground mt-2">
                用户消息右对齐无头像，助手消息左对齐有头像，类似微信聊天界面
              </p>
            </div>

            {/* 平铺样式 */}
            <div 
              className={`
                p-4 border-2 rounded-lg cursor-pointer transition-all
                ${chatStyle === 'flat' 
                  ? 'border-primary bg-primary/5' 
                  : 'border-border hover:border-primary/50'
                }
              `}
              onClick={() => setChatStyle('flat')}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <span className="font-medium">平铺样式</span>
                  {chatStyle === 'flat' && (
                    <Badge variant="default" className="text-xs">当前</Badge>
                  )}
                </div>
                <div className="w-4 h-4 rounded-full border-2 border-primary flex items-center justify-center">
                  {chatStyle === 'flat' && (
                    <div className="w-2 h-2 rounded-full bg-primary" />
                  )}
                </div>
              </div>
              
              {/* 平铺样式预览 */}
              <div className="space-y-3 bg-muted/30 p-3 rounded">
                {/* 用户消息 */}
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-3 w-3 text-primary-foreground" />
                  </div>
                  <div className="flex-1">
                    <div className="text-xs">用户消息示例</div>
                  </div>
                </div>
                
                {/* 助手消息 */}
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot className="h-3 w-3" />
                  </div>
                  <div className="flex-1">
                    <div className="text-xs">助手回复示例</div>
                  </div>
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground mt-2">
                所有消息统一左对齐显示，适合阅读长文本内容
              </p>
            </div>
          </div>

          {/* 样式特性说明 */}
          <div className="bg-muted/30 p-4 rounded-lg">
            <h4 className="font-medium mb-2">样式特性对比</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="font-medium text-primary mb-1">气泡样式</div>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• 用户消息右对齐，无头像</li>
                  <li>• 助手消息左对齐，有头像</li>
                  <li>• 圆角气泡背景</li>
                  <li>• 类似即时通讯</li>
                </ul>
              </div>
              <div>
                <div className="font-medium text-primary mb-1">平铺样式</div>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• 所有消息左对齐</li>
                  <li>• 统一的布局结构</li>
                  <li>• 适合长文本阅读</li>
                  <li>• 当前默认样式</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex justify-end gap-2">
        {saveStatus && (
          <div className="flex items-center gap-2 mr-4">
            {getSaveStatusIcon()}
            <span className={`text-sm ${
              saveStatus === 'success' ? 'text-green-600' : 'text-red-600'
            }`}>
              {saveStatus === 'success' ? '设置已保存' : '保存失败'}
            </span>
          </div>
        )}
        
        <Button variant="outline" onClick={handleReset}>
          <RefreshCw className="h-4 w-4 mr-2" />
          重置
        </Button>
        
        <Button onClick={handleSave} disabled={isSaving}>
          {isSaving ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {isSaving ? '保存中...' : '保存设置'}
        </Button>
      </div>
    </div>
  );
};
