import React, {useEffect, useState} from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Textarea} from '@/components/ui/textarea';
import {Badge} from '@/components/ui/badge';
import {ScrollArea} from '@/components/ui/scroll-area';
import {Separator} from '@/components/ui/separator';
import {Check, Copy, Download, Edit, Plus, Save, Search, Terminal, Trash2, Users, X} from 'lucide-react';
import {useChatStore} from '../../store/chatStore';

export const BasicInfoSettings = () => {
  const { userConfig, updateUserConfig } = useChatStore();
  const [unionIds, setUnionIds] = useState([]);
  const [newUnionId, setNewUnionId] = useState('');
  const [editingId, setEditingId] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [copiedId, setCopiedId] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [commandTemplate, setCommandTemplate] = useState('');

  // 从用户配置中加载 unionIds
  useEffect(() => {
    if (userConfig.unionIds) {
      setUnionIds(userConfig.unionIds);
    }
  }, [userConfig]);

  // 保存 unionIds 到用户配置
  const saveUnionIds = async (newUnionIds) => {
    try {
      await updateUserConfig({ unionIds: newUnionIds });
      setUnionIds(newUnionIds);
    } catch (error) {
      console.error('保存 UnionId 失败:', error);
    }
  };

  // 过滤 UnionId 列表
  const filteredUnionIds = unionIds.filter(item => 
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.value.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // 添加新的 UnionId
  const handleAddUnionId = () => {
    if (!newUnionId.trim()) return;
    
    const newId = {
      id: Date.now().toString(),
      value: newUnionId.trim(),
      name: `用户 ${unionIds.length + 1}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      description: '',
      isActive: true
    };
    
    const updatedIds = [...unionIds, newId];
    saveUnionIds(updatedIds);
    setNewUnionId('');
  };

  // 删除 UnionId
  const handleDeleteUnionId = (id) => {
    const updatedIds = unionIds.filter(item => item.id !== id);
    saveUnionIds(updatedIds);
  };

  // 开始编辑
  const handleStartEdit = (item) => {
    setEditingId(item.id);
    setEditValue(item.value);
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (!editValue.trim()) return;
    
    const updatedIds = unionIds.map(item => 
      item.id === editingId 
        ? { ...item, value: editValue.trim(), updatedAt: new Date() }
        : item
    );
    
    saveUnionIds(updatedIds);
    setEditingId(null);
    setEditValue('');
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingId(null);
    setEditValue('');
  };

  // 复制到剪贴板
  const handleCopy = async (value) => {
    try {
      await navigator.clipboard.writeText(value);
      setCopiedId(value);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 更新名称和描述
  const handleUpdateMeta = (id, field, value) => {
    const updatedIds = unionIds.map(item => 
      item.id === id 
        ? { ...item, [field]: value, updatedAt: new Date() }
        : item
    );
    saveUnionIds(updatedIds);
  };

  // 生成命令模板
  const generateCommandTemplate = () => {
    if (unionIds.length === 0) {
      setCommandTemplate('暂无可用的 UnionId');
      return;
    }

    const template = unionIds.map(item => 
      `/UnionId ${item.value} # ${item.name}${item.description ? ' - ' + item.description : ''}`
    ).join('\n');
    
    setCommandTemplate(template);
  };

  // 导出 UnionId 数据
  const handleExport = () => {
    const csvContent = unionIds.map(item => 
      `${item.value},${item.name},${item.description || ''}`
    ).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `unionids_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* UnionId 管理 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                UnionId 管理
                <Badge variant="secondary" className="ml-2">
                  {unionIds.length} 个
                </Badge>
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                管理个人 UnionId，在指令输入中可以快速选择使用
              </p>
            </div>
            
            {/* 工具按钮 */}
            <div className="flex items-center gap-2">
              {unionIds.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  className="hidden sm:flex"
                >
                  <Download className="h-4 w-4 mr-1" />
                  导出
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 添加新 UnionId */}
          <div className="space-y-2">
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                placeholder="输入新的 UnionId..."
                value={newUnionId}
                onChange={(e) => setNewUnionId(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleAddUnionId();
                  }
                }}
                className="flex-1"
              />
              <Button 
                onClick={handleAddUnionId} 
                disabled={!newUnionId.trim()}
                className="w-full sm:w-auto"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加 UnionId
              </Button>
            </div>

            {/* 搜索框 */}
            {unionIds.length > 3 && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索 UnionId..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}
          </div>

          <Separator />

          {/* 统计信息 */}
          {unionIds.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-xl font-bold text-primary">{unionIds.length}</div>
                  <div className="text-xs text-muted-foreground">总数量</div>
                </div>
              </Card>
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-xl font-bold text-green-600">
                    {unionIds.filter(item => item.isActive !== false).length}
                  </div>
                  <div className="text-xs text-muted-foreground">活跃用户</div>
                </div>
              </Card>
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-xl font-bold text-blue-600">{filteredUnionIds.length}</div>
                  <div className="text-xs text-muted-foreground">搜索结果</div>
                </div>
              </Card>
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-xl font-bold text-orange-600">
                    {unionIds.filter(item => item.description && item.description.trim()).length}
                  </div>
                  <div className="text-xs text-muted-foreground">有描述</div>
                </div>
              </Card>
            </div>
          )}

          {/* UnionId 列表 */}
          <div className="max-h-96 overflow-hidden">
            {filteredUnionIds.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                {searchQuery ? (
                  <>
                    <p>未找到匹配的 UnionId</p>
                    <p className="text-sm">尝试调整搜索条件</p>
                  </>
                ) : (
                  <>
                    <p>暂无 UnionId</p>
                    <p className="text-sm">添加 UnionId 以便在聊天中快速使用</p>
                  </>
                )}
              </div>
            ) : (
              <ScrollArea className="h-96">
                <div className="space-y-3 pr-2">
                  {filteredUnionIds.map((item) => (
                    <Card key={item.id} className="p-4 hover:shadow-md transition-shadow">
                      <div className="space-y-3">
                        {/* 第一行：名称和操作按钮 */}
                        <div className="flex items-center justify-between gap-2">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <Input
                              value={item.name}
                              onChange={(e) => handleUpdateMeta(item.id, 'name', e.target.value)}
                              className="font-medium w-40"
                              placeholder="名称"
                            />
                            <Badge variant="outline" className="text-xs">
                              {new Date(item.createdAt).toLocaleDateString()}
                            </Badge>
                            {item.isActive !== false && (
                              <Badge variant="default" className="text-xs">
                                活跃
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopy(item.value)}
                              className="h-8 w-8 p-0"
                              title="复制 UnionId"
                            >
                              {copiedId === item.value ? (
                                <Check className="h-3 w-3 text-green-600" />
                              ) : (
                                <Copy className="h-3 w-3" />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleStartEdit(item)}
                              className="h-8 w-8 p-0"
                              title="编辑 UnionId"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteUnionId(item.id)}
                              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                              title="删除 UnionId"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        {/* 第二行：UnionId 值 */}
                        <div>
                          <Label className="text-xs text-muted-foreground">UnionId 值</Label>
                          {editingId === item.id ? (
                            <div className="flex gap-2 mt-1">
                              <Input
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="flex-1 font-mono"
                                placeholder="输入 UnionId"
                              />
                              <Button
                                size="sm"
                                onClick={handleSaveEdit}
                                className="h-8 w-8 p-0"
                                disabled={!editValue.trim()}
                              >
                                <Save className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleCancelEdit}
                                className="h-8 w-8 p-0"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ) : (
                            <div className="
                              mt-1 p-2 
                              bg-muted/50 rounded 
                              font-mono 
                              text-sm 
                              break-all
                              border
                              hover:bg-muted/70
                              cursor-pointer
                              transition-colors
                            "
                            onClick={() => handleCopy(item.value)}
                            title="点击复制"
                            >
                              {item.value}
                            </div>
                          )}
                        </div>

                        {/* 第三行：描述 */}
                        <div>
                          <Label className="text-xs text-muted-foreground">描述信息</Label>
                          <Textarea
                            value={item.description || ''}
                            onChange={(e) => handleUpdateMeta(item.id, 'description', e.target.value)}
                            placeholder="添加描述信息，便于识别和管理..."
                            className="mt-1 min-h-[60px]"
                          />
                        </div>

                        {/* 更新时间 */}
                        {item.updatedAt && (
                          <div className="text-xs text-muted-foreground">
                            最后更新：{new Date(item.updatedAt).toLocaleString()}
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 命令模板生成 */}
      {unionIds.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="h-5 w-5" />
              命令模板生成器
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              生成可直接在聊天中使用的批量命令模板
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={generateCommandTemplate} className="w-full">
              <Terminal className="h-4 w-4 mr-2" />
              生成所有 UnionId 的命令模板
            </Button>
            
            {commandTemplate && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="font-medium">生成的命令模板</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopy(commandTemplate)}
                    className="h-8 px-2"
                  >
                    {copiedId === commandTemplate ? (
                      <Check className="h-3 w-3 text-green-600 mr-1" />
                    ) : (
                      <Copy className="h-3 w-3 mr-1" />
                    )}
                    复制全部
                  </Button>
                </div>
                <Textarea
                  value={commandTemplate}
                  readOnly
                  className="
                    font-mono 
                    text-sm 
                    min-h-[120px] 
                    bg-muted/50
                    border-dashed
                  "
                />
                <p className="text-xs text-muted-foreground">
                  💡 复制后可直接粘贴到聊天输入框中使用，系统会自动识别每个 UnionId 命令
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
