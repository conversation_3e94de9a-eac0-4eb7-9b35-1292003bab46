import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, Palette } from 'lucide-react';
import { ChatStyleSettings } from './ChatStyleSettings';

export const ConversationSettings = () => {
  const [activeSection, setActiveSection] = useState('chatStyle');

  const sections = [
    {
      id: 'chatStyle',
      label: '对话样式管理',
      icon: Palette,
      description: '自定义聊天界面的显示样式'
    }
  ];

  return (
    <div className="space-y-6">
      {/* 导航菜单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            对话管理
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            管理对话相关的设置和样式
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {sections.map((section) => (
              <Button
                key={section.id}
                variant={activeSection === section.id ? 'default' : 'outline'}
                onClick={() => setActiveSection(section.id)}
                className="flex items-center gap-2"
              >
                <section.icon className="h-4 w-4" />
                {section.label}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 内容区域 */}
      <div>
        {activeSection === 'chatStyle' && <ChatStyleSettings />}
      </div>
    </div>
  );
};
