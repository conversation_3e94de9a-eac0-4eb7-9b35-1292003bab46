import * as React from 'react';
import {useEffect, useRef} from 'react';
import {ScrollArea} from '@/components/ui/scroll-area';
import {MessageItem} from './MessageItem';
import {Message} from '@/service/types';

// 定义组件 Props 类型
interface MessageListProps {
    messages: Message[];
}

export const MessageList: React.FC<MessageListProps> = ({messages}) => {
    const scrollAreaRef = useRef<HTMLDivElement>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    // 自动滚动到底部
    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({behavior: 'smooth'});
        }
    }, [messages]);

    if (!messages || messages.length === 0) {
        return (
            <div className="h-full flex items-center justify-center text-muted-foreground">
                <div className="text-center space-y-2 px-4">
                    <p className="text-sm sm:text-base">开始您的对话</p>
                    <p className="text-xs sm:text-sm">输入消息或使用 / 命令快速开始</p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full">
            <ScrollArea ref={scrollAreaRef} className="h-full custom-scrollbar">
                <div
                    className="p-2 sm:p-4 lg:p-6 xl:p-8 space-y-4 sm:space-y-6 min-h-full max-w-none sm:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl ultrawide:max-w-8xl mx-auto">
                    {messages.map((message: Message, index: number) => (
                        <MessageItem
                            key={message.id}
                            message={message}
                            isLast={index === messages.length - 1}
                        />
                    ))}
                    <div ref={messagesEndRef}/>
                </div>
            </ScrollArea>
        </div>
    );
};
