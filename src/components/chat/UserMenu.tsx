import * as React from 'react';
import {useEffect, useRef, useState} from 'react';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {LogOut, Monitor, Moon, Palette, Settings, Sun, User} from 'lucide-react';
import {useNavigate} from 'react-router-dom';
import {type Theme, useTheme} from '../theme/ThemeProvider';

// 定义主题选项类型
interface ThemeOption {
    value: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
}

export const UserMenu: React.FC = () => {
    const navigate = useNavigate();
    const {theme, setTheme} = useTheme();
    const [triggerWidth, setTriggerWidth] = useState<number>(0);
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [showThemeOptions, setShowThemeOptions] = useState<boolean>(false);
    const triggerRef = useRef<HTMLDivElement>(null);

    const themeOptions: ThemeOption[] = [
        {value: 'light', label: '浅色', icon: Sun},
        {value: 'dark', label: '深色', icon: Moon},
        {value: 'system', label: '跟随系统', icon: Monitor}
    ];

    const handleSettingsClick = (): void => {
        navigate('/settings');
        setIsOpen(false);
    };

    const handleThemeChange = (newTheme: Theme): void => {
        setTheme(newTheme);
        setIsOpen(false);
        setShowThemeOptions(false);
    };

    const handleThemeMenuClick = (): void => {
        setShowThemeOptions(!showThemeOptions);
    };

    const handleLogout = (): void => {
        // 清除用户数据
        localStorage.removeItem('userConfig');
        localStorage.removeItem('conversations');
        localStorage.removeItem('shares');
        localStorage.removeItem('theme');

        // 刷新页面或重定向到登录页
        window.location.reload();
    };

    // 获取触发器宽度
    useEffect(() => {
        if (triggerRef.current) {
            setTriggerWidth(triggerRef.current.offsetWidth);
        }
    }, []);

    // 检测是否为小屏幕
    const [isSmallScreen, setIsSmallScreen] = useState<boolean>(false);

    useEffect(() => {
        const checkScreenSize = (): void => {
            setIsSmallScreen(window.innerWidth < 640); // sm breakpoint
        };

        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
        return () => window.removeEventListener('resize', checkScreenSize);
    }, []);

    // 当菜单关闭时重置主题选项显示状态
    useEffect(() => {
        if (!isOpen) {
            setShowThemeOptions(false);
        }
    }, [isOpen]);

    return (
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
                <div
                    ref={triggerRef}
                    className="
            flex items-center gap-2 
            p-2 rounded-lg 
            hover:bg-accent 
            cursor-pointer 
            transition-colors
            w-full
            min-h-[44px]
          "
                >
                    <Avatar className="h-7 w-7 flex-shrink-0">
                        <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                            <User className="h-4 w-4"/>
                        </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium truncate">用户</div>
                        <div className="text-xs text-muted-foreground truncate">点击管理</div>
                    </div>
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
                align={isSmallScreen ? "center" : "end"}
                side={isSmallScreen ? "top" : "top"}
                sideOffset={8}
                alignOffset={isSmallScreen ? 0 : undefined}
                className={`
          ${isSmallScreen ? 'w-56' : 'w-48 sm:w-56'}
          ${isSmallScreen ? 'mx-4' : ''}
        `}
                style={{
                    minWidth: `${Math.max(triggerWidth, 192)}px`,
                    ...(isSmallScreen && {
                        position: 'fixed' as const,
                        left: '1rem',
                        right: '1rem',
                        width: 'calc(100vw - 2rem)',
                        maxWidth: '20rem'
                    })
                }}
                avoidCollisions={true}
                collisionPadding={isSmallScreen ? 16 : 8}
            >
                <DropdownMenuItem onClick={handleSettingsClick}>
                    <Settings className="h-4 w-4 mr-2"/>
                    设置
                </DropdownMenuItem>

                <DropdownMenuSeparator/>

                {/* 在小屏幕下直接显示主题选项，避免子菜单 */}
                {isSmallScreen ? (
                    <>
                        <DropdownMenuItem onClick={handleThemeMenuClick}>
                            <Palette className="h-4 w-4 mr-2"/>
                            主题设置
                            <span className="ml-auto text-xs">
                {showThemeOptions ? '▲' : '▼'}
              </span>
                        </DropdownMenuItem>

                        {showThemeOptions && (
                            <>
                                {themeOptions.map((option: ThemeOption) => {
                                    const IconComponent = option.icon;
                                    return (
                                        <DropdownMenuItem
                                            key={option.value}
                                            onClick={() => handleThemeChange(option.value as Theme)}
                                            className={`ml-4 ${theme === option.value ? 'bg-accent' : ''}`}
                                        >
                                            <IconComponent className="h-4 w-4 mr-2"/>
                                            {option.label}
                                            {theme === option.value && (
                                                <span className="ml-auto text-xs">✓</span>
                                            )}
                                        </DropdownMenuItem>
                                    );
                                })}
                            </>
                        )}
                    </>
                ) : (
                    <DropdownMenuSub>
                        <DropdownMenuSubTrigger>
                            <Palette className="h-4 w-4 mr-2"/>
                            主题
                        </DropdownMenuSubTrigger>
                        <DropdownMenuSubContent
                            alignOffset={-4}
                            sideOffset={8}
                            avoidCollisions={true}
                            collisionPadding={8}
                        >
                            {themeOptions.map((option: ThemeOption) => {
                                const IconComponent = option.icon;
                                return (
                                    <DropdownMenuItem
                                        key={option.value}
                                        onClick={() => handleThemeChange(option.value as Theme)}
                                        className={theme === option.value ? 'bg-accent' : ''}
                                    >
                                        <IconComponent className="h-4 w-4 mr-2"/>
                                        {option.label}
                                        {theme === option.value && (
                                            <span className="ml-auto text-xs">✓</span>
                                        )}
                                    </DropdownMenuItem>
                                );
                            })}
                        </DropdownMenuSubContent>
                    </DropdownMenuSub>
                )}

                <DropdownMenuSeparator/>

                <DropdownMenuItem
                    onClick={handleLogout}
                    className="text-destructive focus:text-destructive"
                >
                    <LogOut className="h-4 w-4 mr-2"/>
                    退出登录
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};
