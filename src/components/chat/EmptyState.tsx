import React from 'react';
import {Card, CardContent} from '@/components/ui/card';
import {BarChart3, MessageSquare, Search, Zap} from 'lucide-react';

// 定义功能项类型
interface FeatureItem {
    icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
    title: string;
    description: string;
}

export const EmptyState: React.FC = () => {
    const features: FeatureItem[] = [
        {
            icon: Search,
            title: 'TraceId 分析',
            description: '输入 /TraceId 快速分析调用链路，识别性能瓶颈'
        },
        {
            icon: BarChart3,
            title: 'UnionId 查询',
            description: '输入 /UnionId 查看用户行为数据和异常检测'
        },
        {
            icon: Zap,
            title: 'FSD 链路',
            description: '输入 /FSD 选择服务依赖关系图进行分析'
        }
    ];

    return (
        <div className="h-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
            <div
                className="max-w-xs sm:max-w-lg lg:max-w-2xl xl:max-w-4xl w-full text-center space-y-4 sm:space-y-6 lg:space-y-8">
                {/* 主标题 - 响应式大小 */}
                <div className="space-y-2 sm:space-y-3 lg:space-y-4">
                    <div
                        className="mx-auto w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-primary/10 rounded-full flex items-center justify-center">
                        <MessageSquare className="h-6 w-6 sm:h-8 sm:w-8 lg:h-10 lg:w-10 text-primary"/>
                    </div>
                    <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight">
                        端到端测试智能体平台
                    </h1>
                    <p className="text-sm sm:text-base lg:text-lg xl:text-xl text-muted-foreground leading-relaxed px-2 sm:px-4">
                        基于 LLM 的智能测试分析助手，支持链路追踪、用户行为分析和服务依赖可视化
                    </p>
                </div>

                {/* 功能特性 - 响应式网格 */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                    {features.map((feature, index) => (
                        <Card key={index} className="text-left">
                            <CardContent className="p-3 sm:p-4 lg:p-6">
                                <div className="flex items-start gap-2 sm:gap-3 lg:gap-4">
                                    <div
                                        className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <feature.icon className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-primary"/>
                                    </div>
                                    <div className="space-y-1 sm:space-y-2 min-w-0">
                                        <h3 className="text-sm sm:text-base lg:text-lg font-semibold leading-tight">
                                            {feature.title}
                                        </h3>
                                        <p className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-relaxed">
                                            {feature.description}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* 使用提示 - 响应式大小 */}
                <div className="space-y-2 sm:space-y-3 lg:space-y-4">
                    <p className="text-sm sm:text-base lg:text-lg text-muted-foreground px-2 sm:px-4">
                        在下方输入框中开始对话，或输入{' '}
                        <code className="bg-muted px-1 py-0.5 rounded text-xs sm:text-sm">
                            /
                        </code>
                        {' '}快速触发指令
                    </p>
                </div>
            </div>
        </div>
    );
};
