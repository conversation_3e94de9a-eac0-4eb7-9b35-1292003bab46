import * as React from 'react';
import {Avatar, AvatarFallback} from '@/components/ui/avatar';
import {Button} from '@/components/ui/button';
import {Badge} from '@/components/ui/badge';
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger,} from '@/components/ui/tooltip';
import {
    Bot,
    CheckCircle,
    ChevronLeft,
    ChevronRight,
    Clock,
    Copy,
    Loader2,
    RefreshCw,
    User,
    XCircle
} from 'lucide-react';
import {useChatStore} from '@/store/chatStore';
import {MessageContent} from './MessageContent';
import {CommandBlock, Message} from '@/service/types';

// 定义消息项的 Props 类型
interface MessageItemProps {
    message: Message;
    isLast: boolean;
}

export const MessageItem: React.FC<MessageItemProps> = ({message, isLast}) => {
    const {regenerateMessage, switchMessageSibling, isGenerating, userConfig} = useChatStore();

    const isUser: boolean = message.role === 'user';
    const isStreaming: boolean = message.status === 'streaming';
    const hasSiblings: boolean = Boolean(message.siblings && message.siblings.length > 1);
    const currentSiblingIndex: number = message.currentSiblingIndex || 0;
    const chatStyle: string = userConfig.chatStyle || 'flat'; // 默认为平铺样式

    // 获取当前显示的消息（考虑 siblings）
    const currentMessage: Message = hasSiblings && message.siblings
        ? message.siblings[currentSiblingIndex] || message
        : message;

    const handleCopy = (): void => {
        navigator.clipboard.writeText(currentMessage.content);
    };

    const handleRegenerate = (): void => {
        if (!isGenerating) {
            regenerateMessage(message.id);
        }
    };

    const handleSiblingSwitch = (direction: 'prev' | 'next'): void => {
        switchMessageSibling(message.id, direction);
    };

    const formatTime = (timestamp: Date): string => {
        return timestamp.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusIcon = (): React.ReactNode => {
        switch (currentMessage.status) {
            case 'sending':
                return <Clock className="h-2.5 w-2.5 sm:h-3 sm:w-3 animate-pulse"/>;
            case 'streaming':
                return <Loader2 className="h-2.5 w-2.5 sm:h-3 sm:w-3 animate-spin"/>;
            case 'completed':
                return <CheckCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-status-success"/>;
            case 'error':
                return <XCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-status-error"/>;
            default:
                return <Clock className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>;
        }
    };

    // 气泡样式渲染
    if (chatStyle === 'bubble') {
        return (
            <div className={`flex mb-4 sm:mb-6 ${isUser ? 'justify-end' : 'justify-start'}`}>
                <div
                    className={
                        `flex items-start gap-2 sm:gap-3 
                        ${isUser ?
                            'max-w-[85%] sm:max-w-[75%] flex-row-reverse' :
                            'max-w-full w-full flex-row'
                        }`
                    }>
                    {/* 头像 - 只显示助手头像，不显示用户头像 */}
                    {!isUser && (
                        <Avatar className="h-6 w-6 sm:h-8 sm:w-8 lg:h-10 lg:w-10 flex-shrink-0 mt-1">
                            <AvatarFallback className="bg-muted">
                                <Bot className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5"/>
                            </AvatarFallback>
                        </Avatar>
                    )}

                    {/* 消息气泡 */}
                    <div className={`flex-1 min-w-0 ${isUser ? '' : 'w-full'}`}>
                        <div className={
                            `px-3 sm:px-4 py-2 sm:py-3 rounded-2xl
                                ${isUser
                                ? 'bg-muted/50 text-foreground rounded-br-md w-full'
                                : 'bg-card border rounded-bl-md w-full'
                            }`
                        }>
                            {/* 命令块显示 - 只在用户消息中显示 */}
                            {isUser && message.commandBlocks && message.commandBlocks.length > 0 && (
                                <div className="flex flex-wrap gap-1 sm:gap-2 mb-2">
                                    {message.commandBlocks.map((block: CommandBlock) => (
                                        <Badge
                                            key={block.id}
                                            variant="outline"
                                            className="text-xs bg-primary/10 text-primary border-primary/30 hover:bg-primary/20 transition-colors"
                                        >
                                            {block.type}: {block.displayValue || block.value}
                                        </Badge>
                                    ))}
                                </div>
                            )}

                            {/* 消息内容 */}
                            {isUser ? (
                                <div className="text-sm sm:text-base leading-relaxed">
                                    {currentMessage.content}
                                </div>
                            ) : (
                                <MessageContent
                                    content={currentMessage.content}
                                    isStreaming={isStreaming}
                                    toolResult={currentMessage.toolResult}
                                />
                            )}
                        </div>

                        {/* 消息元信息和操作按钮 */}
                        <div className={`
              flex items-center justify-between text-xs text-muted-foreground 
              flex-wrap sm:flex-nowrap gap-2 mt-1 px-1
            `}>
                            {/* 操作按钮 - 左侧 */}
                            <div className="flex items-center gap-0.5 sm:gap-1">
                                {/* Sibling 导航 - 只在助手消息中显示 */}
                                {!isUser && hasSiblings && (
                                    <div className="flex items-center gap-0.5 sm:gap-1 mr-1 sm:mr-2">
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                                        onClick={() => handleSiblingSwitch('prev')}
                                                        disabled={currentSiblingIndex === 0}
                                                    >
                                                        <ChevronLeft className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>上一个回答</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                        <span className="text-xs px-1">
                      {currentSiblingIndex + 1}/{message.siblings?.length || 0}
                    </span>
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                                        onClick={() => handleSiblingSwitch('next')}
                                                        disabled={currentSiblingIndex === (message.siblings?.length || 1) - 1}
                                                    >
                                                        <ChevronRight className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>下一个回答</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                )}

                                {/* 复制按钮 */}
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                                onClick={handleCopy}
                                            >
                                                <Copy className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>复制消息</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>

                                {/* 重新生成按钮 - 只在助手消息的最后一条显示 */}
                                {!isUser && isLast && !isStreaming && (
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                                    onClick={handleRegenerate}
                                                    disabled={isGenerating}
                                                >
                                                    <RefreshCw className={
                                                        `h-2.5 w-2.5 sm:h-3 sm:w-3 
                                                        ${isGenerating ?
                                                            'animate-spin'
                                                            : ''}
                    `}/>
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>重新生成回答</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                )}
                            </div>

                            {/* 时间 - 右侧 */}
                            <div className="flex items-center gap-1">
                                {getStatusIcon()}
                                <span>{formatTime(currentMessage.timestamp)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // 平铺样式渲染（原有样式）
    return (
        <div className="flex gap-2 sm:gap-3 lg:gap-4 mb-4 sm:mb-6">
            {/* 头像 */}
            <Avatar className="
        h-6 w-6 sm:h-8 sm:w-8 lg:h-10 lg:w-10
        flex-shrink-0
        mt-1
      ">
                <AvatarFallback className={`${isUser ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
                    {isUser ? (
                        <User className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5"/>
                    ) : (
                        <Bot className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5"/>
                    )}
                </AvatarFallback>
            </Avatar>

            {/* 消息内容区域 - 铺满到右边 */}
            <div className="flex-1 min-w-0">
                <div className="space-y-2 sm:space-y-3">
                    {/* 命令块显示 - 只在用户消息中显示 */}
                    {isUser && message.commandBlocks && message.commandBlocks.length > 0 && (
                        <div className="flex flex-wrap gap-1 sm:gap-2">
                            {message.commandBlocks.map((block: CommandBlock) => (
                                <Badge
                                    key={block.id}
                                    variant="outline"
                                    className="text-xs bg-primary/10 text-primary border-primary/30 hover:bg-primary/20 transition-colors"
                                >
                                    {block.type}: {block.displayValue || block.value}
                                </Badge>
                            ))}
                        </div>
                    )}

                    {/* 消息内容 */}
                    {isUser ? (
                        <div className="text-sm sm:text-base leading-relaxed">
                            {currentMessage.content}
                        </div>
                    ) : (
                        <MessageContent
                            content={currentMessage.content}
                            isStreaming={isStreaming}
                            toolResult={currentMessage.toolResult}
                        />
                    )}

                    {/* 消息元信息和操作按钮 */}
                    <div
                        className="flex items-center justify-between text-xs text-muted-foreground flex-wrap sm:flex-nowrap gap-2">
                        <div className="flex items-center gap-1">
                            {getStatusIcon()}
                            <span>{formatTime(currentMessage.timestamp)}</span>
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex items-center gap-0.5 sm:gap-1">
                            {/* Sibling 导航 - 只在助手消息中显示 */}
                            {!isUser && hasSiblings && (
                                <div className="flex items-center gap-0.5 sm:gap-1 mr-1 sm:mr-2">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                                    onClick={() => handleSiblingSwitch('prev')}
                                                    disabled={currentSiblingIndex === 0}
                                                >
                                                    <ChevronLeft className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>上一个回答</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                    <span className="text-xs px-1">
                    {currentSiblingIndex + 1}/{message.siblings?.length || 0}
                  </span>
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                                    onClick={() => handleSiblingSwitch('next')}
                                                    disabled={currentSiblingIndex === (message.siblings?.length || 1) - 1}
                                                >
                                                    <ChevronRight className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>下一个回答</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                            )}

                            {/* 复制按钮 */}
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                            onClick={handleCopy}
                                        >
                                            <Copy className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>复制消息</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>

                            {/* 重新生成按钮 - 只在助手消息的最后一条显示 */}
                            {!isUser && isLast && !isStreaming && (
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 p-0"
                                                onClick={handleRegenerate}
                                                disabled={isGenerating}
                                            >
                                                <RefreshCw className={`
                    h-2.5 w-2.5 sm:h-3 sm:w-3 
                    ${isGenerating ? 'animate-spin' : ''}
                  `}/>
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>重新生成回答</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
