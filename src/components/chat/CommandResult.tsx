import * as React from 'react';
import {<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {Button} from '@/components/ui/button';
import {CheckCircle, Clock, Copy, FileText, Terminal, XCircle} from 'lucide-react';

// 定义命令执行结果类型
interface CommandResult {
    type?: string;
    status: 'success' | 'error' | 'running' | 'pending';
    output?: string;
    error?: string;
    duration?: number;
    command?: string;
}

// 定义组件 Props 类型
interface CommandResultProps {
    result: CommandResult;
}

export const CommandResult: React.FC<CommandResultProps> = ({result}) => {
    const {type, status, output, error, duration, command} = result;

    const handleCopy = () => {
        navigator.clipboard.writeText(output || error || '');
    };

    const getStatusIcon = () => {
        switch (status) {
            case 'success':
                return <CheckCircle className="h-4 w-4 text-status-success"/>;
            case 'error':
                return <XCircle className="h-4 w-4 text-status-error"/>;
            case 'running':
                return <Clock className="h-4 w-4 text-status-pending animate-spin"/>;
            default:
                return <Terminal className="h-4 w-4"/>;
        }
    };

    const getStatusColor = () => {
        switch (status) {
            case 'success':
                return 'border-status-success/30 bg-status-success/5';
            case 'error':
                return 'border-status-error/30 bg-status-error/5';
            case 'running':
                return 'border-status-pending/30 bg-status-pending/5';
            default:
                return 'border-border';
        }
    };

    return (
        <Card className={`mt-3 ${getStatusColor()}`}>
            <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        {getStatusIcon()}
                        <span>命令执行结果</span>
                        <Badge variant="outline" className="text-xs">
                            {type || 'Python'}
                        </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                        {duration && (
                            <span className="text-xs text-muted-foreground">
                {duration}ms
              </span>
                        )}
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={handleCopy}
                        >
                            <Copy className="h-3 w-3"/>
                        </Button>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
                {/* 执行的命令 */}
                {command && (
                    <div className="space-y-1">
                        <div className="text-xs font-medium text-muted-foreground">执行命令:</div>
                        <div className="bg-muted/50 rounded p-2 font-mono text-sm">
                            {command}
                        </div>
                    </div>
                )}

                {/* 输出结果 */}
                {output && (
                    <div className="space-y-1">
                        <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                            <FileText className="h-3 w-3"/>
                            输出结果:
                        </div>
                        <div
                            className="bg-muted/50 rounded p-3 font-mono text-sm max-h-60 overflow-y-auto custom-scrollbar">
                            <pre className="whitespace-pre-wrap">{output}</pre>
                        </div>
                    </div>
                )}

                {/* 错误信息 */}
                {error && (
                    <div className="space-y-1">
                        <div className="text-xs font-medium text-status-error flex items-center gap-1">
                            <XCircle className="h-3 w-3"/>
                            错误信息:
                        </div>
                        <div
                            className="bg-status-error/10 border border-status-error/20 rounded p-3 font-mono text-sm max-h-60 overflow-y-auto custom-scrollbar">
                            <pre className="whitespace-pre-wrap text-status-error">{error}</pre>
                        </div>
                    </div>
                )}

                {/* 运行状态 */}
                {status === 'running' && (
                    <div className="flex items-center gap-2 text-status-pending">
                        <Clock className="h-4 w-4 animate-spin"/>
                        <span className="text-sm">正在执行中...</span>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
