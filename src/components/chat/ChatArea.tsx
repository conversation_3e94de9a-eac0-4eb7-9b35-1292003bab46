import React from 'react';
import {useChatStore} from '@/store/chatStore.ts';
import {MessageList} from './MessageList';
import {EnhancedChatInput} from './EnhancedChatInput';
import {EmptyState} from './EmptyState';

export const ChatArea: React.FC = () => {
    const {currentConversation} = useChatStore();

    return (
        <div className="h-full flex flex-col relative">
            {/* 消息列表或欢迎页面 - 占据除输入框外的所有空间 */}
            <div className="flex-1 overflow-hidden pb-4">
                {currentConversation ? (
                    <MessageList messages={currentConversation.messages}/>
                ) : (
                    <EmptyState/>
                )}
            </div>

            {/* 增强的输入区域 - 固定在底部，始终显示 */}
            <div className="flex-shrink-0 border-t border-border bg-background/95 backdrop-blur-sm">
                <EnhancedChatInput/>
            </div>
        </div>
    );
};
