import * as React from 'react';
import {useRef, useState} from 'react';
import {useChatStore} from '@/store/chatStore.ts';
import {Button} from '@/components/ui/button';
import {Textarea} from '@/components/ui/textarea';
import {Card, CardContent} from '@/components/ui/card';
import {ExternalLink, GripVertical, Hash, Link, Paperclip, Send, Square, User, X} from 'lucide-react';
import {CommandPicker} from './CommandPicker';

// 定义命令块类型
interface CommandBlock {
    id: string;
    type: 'TraceId' | 'UnionId' | 'FSD';
    value: string;
    displayValue?: string;
}

// 定义命令类型
interface Command {
    type: string;
    label: string;
    value?: string;
    displayValue?: string;
}

export const ChatInput: React.FC = () => {
    const {sendMessage, stopGeneration, isGenerating} = useChatStore();
    const [input, setInput] = useState<string>('');
    const [commandBlocks, setCommandBlocks] = useState<CommandBlock[]>([]);
    const [showCommandPicker, setShowCommandPicker] = useState<boolean>(false);
    const [, setCursorPosition] = useState<number>(0);
    const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // 处理输入变化
    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
        const value: string = e.target.value;
        const position: number = e.target.selectionStart;

        setInput(value);
        setCursorPosition(position);

        // 检查是否输入了 / 命令
        if (value.charAt(position - 1) === '/') {
            setShowCommandPicker(true);
        } else if (showCommandPicker && !value.includes('/')) {
            setShowCommandPicker(false);
        }
    };

    // 处理键盘事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>): void => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        } else if (e.key === 'Escape') {
            setShowCommandPicker(false);
        }
    };

    // 添加命令块
    const addCommandBlock = (command: Command): void => {
        const newBlock: CommandBlock = {
            id: Date.now().toString(),
            type: command.type as CommandBlock['type'],
            value: command.value || '',
            displayValue: command.displayValue || command.label
        };

        setCommandBlocks(prev => [...prev, newBlock]);

        // 移除输入框中的 / 字符
        const newInput: string = input.replace('/', '');
        setInput(newInput);
        setShowCommandPicker(false);

        // 聚焦到输入框
        setTimeout(() => {
            textareaRef.current?.focus();
        }, 0);
    };

    // 移除命令块
    const removeCommandBlock = (blockId: string): void => {
        setCommandBlocks(prev => prev.filter(block => block.id !== blockId));
    };

    // 更新命令块值
    const updateCommandBlock = (blockId: string, value: string): void => {
        setCommandBlocks(prev =>
            prev.map(block =>
                block.id === blockId
                    ? {...block, value, displayValue: `${block.type}: ${value}`}
                    : block
            )
        );
    };

    // 拖拽开始
    const handleDragStart = (e: React.DragEvent<HTMLDivElement>, index: number): void => {
        setDraggedIndex(index);
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.currentTarget.outerHTML);
    };

    // 拖拽结束
    const handleDragEnd = (): void => {
        setDraggedIndex(null);
    };

    // 拖拽悬停
    const handleDragOver = (e: React.DragEvent<HTMLDivElement>): void => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    };

    // 拖拽放置
    const handleDrop = (e: React.DragEvent<HTMLDivElement>, dropIndex: number): void => {
        e.preventDefault();

        if (draggedIndex === null || draggedIndex === dropIndex) return;

        const newCommandBlocks: CommandBlock[] = [...commandBlocks];
        const draggedItem: CommandBlock = newCommandBlocks[draggedIndex];

        // 移除拖拽的项目
        newCommandBlocks.splice(draggedIndex, 1);

        // 在新位置插入
        const insertIndex: number = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
        newCommandBlocks.splice(insertIndex, 0, draggedItem);

        setCommandBlocks(newCommandBlocks);
        setDraggedIndex(null);
    };

    // 发送消息
    const handleSend = (): void => {
        if ((!input.trim() && commandBlocks.length === 0) || isGenerating) {
            return;
        }

        // 验证命令块是否都有值（FSD 类型除外，因为它们已经有完整的链接）
        const validCommandBlocks: CommandBlock[] = commandBlocks.filter(block =>
            block.type === 'FSD' || block.value.trim()
        );

        // 去重处理 - 确保不会有重复的命令块
        const uniqueCommandBlocks: CommandBlock[] = validCommandBlocks.reduce<CommandBlock[]>((acc, current) => {
            const existing: CommandBlock | undefined = acc.find(item =>
                item.type === current.type &&
                item.value === current.value
            );
            if (!existing) {
                acc.push(current);
            }
            return acc;
        }, []);

        sendMessage(input.trim(), uniqueCommandBlocks);
        setInput('');
        setCommandBlocks([]);
        setShowCommandPicker(false);
    };

    // 停止生成
    const handleStop = (): void => {
        stopGeneration();
    };

    // 获取命令块图标
    const getCommandIcon = (type: CommandBlock['type']): React.ReactNode => {
        switch (type) {
            case 'TraceId':
                return <Hash className="h-3 w-3"/>;
            case 'UnionId':
                return <User className="h-3 w-3"/>;
            case 'FSD':
                return <Link className="h-3 w-3"/>;
            default:
                return <Hash className="h-3 w-3"/>;
        }
    };

    // 渲染命令块
    const renderCommandBlock = (block: CommandBlock, index: number): React.ReactNode => {
        if (block.type === 'FSD') {
            // FSD 类型显示为链接卡片，移除重复的前缀
            const displayName: string = block.displayValue ? block.displayValue.replace(/^FSD:\s*/, '') : block.value;

            return (
                <Card
                    key={block.id}
                    className={`relative cursor-move transition-all duration-200 ${
                        draggedIndex === index ? 'opacity-50 scale-95' : 'hover:shadow-md'
                    }`}
                    draggable
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDragEnd={handleDragEnd}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, index)}
                >
                    <CardContent className="p-3">
                        <div className="flex items-center gap-3">
                            <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab"/>
                            <div className="flex items-center gap-2">
                                <Link className="h-4 w-4 text-primary"/>
                                <div className="flex-1">
                                    <div className="text-sm font-medium">{displayName}</div>
                                    <div className="text-xs text-muted-foreground flex items-center gap-1">
                                        <span>FSD 服务链路</span>
                                        <ExternalLink className="h-3 w-3"/>
                                    </div>
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => removeCommandBlock(block.id)}
                            >
                                <X className="h-3 w-3"/>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            );
        }

        // TraceId 和 UnionId 类型显示为输入框，移除重复的前缀
        return (
            <Card
                key={block.id}
                className={`relative cursor-move transition-all duration-200 ${
                    draggedIndex === index ? 'opacity-50 scale-95' : ''
                }`}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
            >
                <CardContent className="p-2">
                    <div className="flex items-center gap-2">
                        <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab"/>
                        {getCommandIcon(block.type)}
                        <span className="text-sm font-medium">{block.type}</span>
                        <input
                            type="text"
                            placeholder={`输入 ${block.type}`}
                            value={block.value}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateCommandBlock(block.id, e.target.value)}
                            className="bg-transparent border-none outline-none text-sm min-w-[120px] flex-1"
                        />
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0"
                            onClick={() => removeCommandBlock(block.id)}
                        >
                            <X className="h-3 w-3"/>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    };

    return (
        <div className="relative w-full">
            {/* 命令选择器 */}
            {showCommandPicker && (
                <div className="absolute bottom-full left-0 right-0 mb-2 z-50">
                    <CommandPicker
                        onSelect={addCommandBlock}
                        onClose={() => setShowCommandPicker(false)}
                    />
                </div>
            )}

            <div className="w-full p-4 space-y-3">
                {/* 命令块显示 */}
                {commandBlocks.length > 0 && (
                    <div className="space-y-2">
                        {commandBlocks.map((block, index) => renderCommandBlock(block, index))}
                    </div>
                )}

                {/* 输入区域 */}
                <div className="flex gap-2 w-full">
                    <div className="flex-1 relative">
                        <Textarea
                            ref={textareaRef}
                            value={input}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            placeholder="输入消息... (输入 / 可以快速添加命令)"
                            className="min-h-[60px] max-h-[200px] resize-none pr-12 w-full"
                            disabled={isGenerating}
                        />

                        {/* 附件按钮 */}
                        <Button
                            variant="ghost"
                            size="sm"
                            className="absolute right-2 top-2 h-8 w-8 p-0"
                            disabled={isGenerating}
                        >
                            <Paperclip className="h-4 w-4"/>
                        </Button>
                    </div>

                    {/* 发送/停止按钮 */}
                    {isGenerating ? (
                        <Button
                            onClick={handleStop}
                            variant="outline"
                            size="sm"
                            className="px-3"
                        >
                            <Square className="h-4 w-4"/>
                        </Button>
                    ) : (
                        <Button
                            onClick={handleSend}
                            disabled={!input.trim() && commandBlocks.length === 0}
                            size="sm"
                            className="px-3"
                        >
                            <Send className="h-4 w-4"/>
                        </Button>
                    )}
                </div>

                {/* 提示文本 */}
                <div className="text-xs text-muted-foreground">
                    按 Enter 发送，Shift + Enter 换行 • 拖拽命令块可以调整顺序
                </div>
            </div>
        </div>
    );
};
