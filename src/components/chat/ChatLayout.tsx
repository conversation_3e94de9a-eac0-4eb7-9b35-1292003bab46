import React from 'react';
import {useChatStore} from '@/store/chatStore.ts';
import {Sidebar} from './Sidebar';
import {ChatArea} from './ChatArea';
import {Button} from '@/components/ui/button';
import {PanelLeftOpen} from 'lucide-react';

export const ChatLayout: React.FC = () => {
    const {sidebarCollapsed, setSidebarCollapsed} = useChatStore();

    return (
        <div className="flex h-screen overflow-hidden">
            {/* 侧边栏 - 修复小屏幕样式 */}
            <div className={`
                ${sidebarCollapsed ? 'w-0' : 'w-full sm:w-80 lg:w-88 xl:w-96'} 
                transition-all duration-300 border-r border-border bg-card h-full flex-shrink-0
                ${!sidebarCollapsed ? 'fixed sm:relative z-50 sm:z-auto' : 'hidden'}
                ${!sidebarCollapsed ? 'inset-0 sm:inset-auto' : ''}
            `}>
                <Sidebar/>
            </div>

            {/* 主聊天区域 - 占据剩余空间 */}
            <div className="flex-1 flex flex-col h-full min-w-0">
                {/* 顶部工具栏 - 响应式高度和间距 */}
                <div
                    className="h-12 sm:h-14 lg:h-16 shadow-[0_1px_0_0_hsl(var(--border))] bg-card/50 backdrop-blur-sm flex items-center px-2 sm:px-4 lg:px-6 flex-shrink-0">
                    {/* 只有在左边栏收起时才显示收起按钮 */}
                    {sidebarCollapsed && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                            className="mr-2 h-8 w-8 p-0 sm:h-9 sm:w-9 lg:h-10 lg:w-10"
                        >
                            <PanelLeftOpen className="h-4 w-4 sm:h-5 sm:w-5"/>
                        </Button>
                    )}

                    <div className="flex-1 min-w-0">
                        <h1 className="text-sm sm:text-base lg:text-lg xl:text-xl font-semibold truncate">
                            端到端测试智能体平台
                        </h1>
                    </div>
                </div>

                {/* 聊天内容区域 - 占据剩余高度，响应式布局 */}
                <div className="flex-1 overflow-hidden">
                    <ChatArea/>
                </div>
            </div>

            {/* 移动端侧边栏遮罩 - 修复遮罩层级和点击区域 */}
            {!sidebarCollapsed && (
                <div className="fixed inset-0 bg-black/50 z-40 sm:hidden backdrop-blur-sm"
                     onClick={() => setSidebarCollapsed(true)}
                />
            )}
        </div>
    );
};
