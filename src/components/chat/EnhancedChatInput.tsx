import * as React from 'react';
import {useRef, useState} from 'react';
import {useChatStore} from '@/store/chatStore.ts';
import {Button} from '@/components/ui/button';
import {Card, CardContent} from '@/components/ui/card';
import {Textarea} from '@/components/ui/textarea';
import {ExternalLink, GripVertical, Hash, Link, Paperclip, Send, Square, Terminal, User, X} from 'lucide-react';
import {CommandResult} from './CommandResult';
import {CommandPicker} from './CommandPicker';
import {v4 as uuid} from 'uuid';

// 定义命令块类型
interface CommandBlock {
    id: string;
    type: 'TraceId' | 'UnionId' | 'FSD';
    value: string;
    displayValue?: string;
}

// 定义命令类型
interface Command {
    type: string;
    label: string;
    value?: string;
    displayValue?: string;
}

// 定义命令执行结果类型
interface CommandExecutionResult {
    id: string;
    type: string;
    status: 'success' | 'error' | 'running' | 'pending';
    command: string;
    output: string;
    error?: string;
    duration?: number;
}

// 定义模拟结果类型
interface MockResult {
    success: boolean;
    output?: string;
    error?: string;
}

export const EnhancedChatInput: React.FC = () => {
    const {sendMessage, stopGeneration, isGenerating} = useChatStore();
    const [input, setInput] = useState<string>('');
    const [commandBlocks, setCommandBlocks] = useState<CommandBlock[]>([]);
    const [showCommandPicker, setShowCommandPicker] = useState<boolean>(false);
    const [, setCursorPosition] = useState<number>(0);
    const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
    const [commandResults, setCommandResults] = useState<CommandExecutionResult[]>([]);
    const [isExecutingCommand, setIsExecutingCommand] = useState<boolean>(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // 处理输入变化
    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        const position = e.target.selectionStart;

        setInput(value);
        setCursorPosition(position);

        // 检查是否输入了 / 命令
        if (value.charAt(position - 1) === '/') {
            setShowCommandPicker(true);
        } else if (showCommandPicker && !value.includes('/')) {
            setShowCommandPicker(false);
        }
    };

    // 处理键盘事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        } else if (e.key === 'Escape') {
            setShowCommandPicker(false);
        }
    };

    // 检测并执行代码块
    const detectAndExecuteCode = (text: string): boolean => {
        // 检测 Python 代码模式
        const pythonPatterns = [
            /执行代码[：:]\s*(.+)/i,
            /运行[：:]\s*(.+)/i,
            /python[：:]?\s*(.+)/i,
            /^(.+)$/  // 如果输入看起来像代码，也尝试执行
        ];

        for (const pattern of pythonPatterns) {
            const match = text.match(pattern);
            if (match) {
                const code = match[1] || match[0];
                // 简单判断是否为代码（包含常见的 Python 关键字或语法）
                if (code.includes('import ') ||
                    code.includes('def ') ||
                    code.includes('print(') ||
                    code.includes('for ') ||
                    code.includes('if ') ||
                    code.includes('=') ||
                    code.includes('requests.') ||
                    code.includes('.get(') ||
                    code.includes('.post(')) {
                    executeCode(code, 'python');
                    return true;
                }
            }
        }
        return false;
    };

    // 模拟代码执行
    const executeCode = async (code: string, language: string = 'python'): Promise<void> => {
        const resultId = uuid();
        const startTime = Date.now();

        // 添加执行中的结果
        const runningResult: CommandExecutionResult = {
            id: resultId,
            type: language,
            status: 'running',
            command: code,
            output: ''
        };

        setCommandResults(prev => [...prev, runningResult]);
        setIsExecutingCommand(true);

        try {
            // 模拟执行延迟
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

            // 模拟执行结果
            const mockResult = generateMockResult(code);
            const duration = Date.now() - startTime;

            const completedResult: CommandExecutionResult = {
                ...runningResult,
                status: mockResult.success ? 'success' : 'error',
                output: mockResult.success ? (mockResult.output || '') : '',
                ...(mockResult.success ? {} : {error: mockResult.error || '执行失败'}),
                duration
            };

            setCommandResults(prev =>
                prev.map(result =>
                    result.id === resultId ? completedResult : result
                )
            );

        } catch (error) {
            const duration = Date.now() - startTime;
            const errorResult: CommandExecutionResult = {
                ...runningResult,
                status: 'error',
                output: '',
                error: (error as Error).message || '执行失败',
                duration
            };

            setCommandResults(prev =>
                prev.map(result =>
                    result.id === resultId ? errorResult : result
                )
            );
        } finally {
            setIsExecutingCommand(false);
        }
    };

    // 生成模拟执行结果
    const generateMockResult = (code: string): MockResult => {
        // 根据代码内容生成相应的模拟结果
        if (code.includes('requests') && code.includes('get')) {
            return {
                success: true,
                output: `HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 1234

{
  "status": "success",
  "data": {
    "urls": [
      "https://example.com/report1.html",
      "https://example.com/report2.html",
      "https://example.com/report3.html"
    ],
    "total": 3,
    "timestamp": "${new Date().toISOString()}"
  }
}`
            };
        }

        if (code.includes('print')) {
            const printMatch = code.match(/print\(['"](.+?)['"]\)/);
            if (printMatch) {
                return {
                    success: true,
                    output: printMatch[1]
                };
            }
        }

        if (code.includes('import')) {
            return {
                success: true,
                output: '模块导入成功'
            };
        }

        if (code.includes('for') || code.includes('while')) {
            return {
                success: true,
                output: `循环执行完成
处理了 ${Math.floor(Math.random() * 100) + 1} 个项目
耗时: ${(Math.random() * 5).toFixed(2)}秒`
            };
        }

        // 随机生成成功或失败
        const isSuccess = Math.random() > 0.2; // 80% 成功率

        if (isSuccess) {
            return {
                success: true,
                output: `执行成功
结果: ${Math.floor(Math.random() * 1000)}
状态: 完成`
            };
        } else {
            return {
                success: false,
                error: `NameError: name 'undefined_variable' is not defined
  File "<stdin>", line 1, in <module>
    ${code}
    ^
SyntaxError: 语法错误或变量未定义`
            };
        }
    };

    // 添加命令块
    const addCommandBlock = (command: Command) => {
        const newBlock: CommandBlock = {
            id: Date.now().toString(),
            type: command.type as CommandBlock['type'],
            value: command.value || '',
            displayValue: command.displayValue || command.label
        };

        setCommandBlocks(prev => [...prev, newBlock]);

        // 移除输入框中的 / 字符
        const newInput = input.replace('/', '');
        setInput(newInput);
        setShowCommandPicker(false);

        // 聚焦到输入框
        setTimeout(() => {
            textareaRef.current?.focus();
        }, 0);
    };

    // 移除命令块
    const removeCommandBlock = (blockId: string) => {
        setCommandBlocks(prev => prev.filter(block => block.id !== blockId));
    };

    // 移除命令结果
    const removeCommandResult = (resultId: string) => {
        setCommandResults(prev => prev.filter(result => result.id !== resultId));
    };

    // 更新命令块值
    const updateCommandBlock = (blockId: string, value: string) => {
        setCommandBlocks(prev =>
            prev.map(block =>
                block.id === blockId
                    ? {...block, value, displayValue: `${block.type}: ${value}`}
                    : block
            )
        );
    };

    // 拖拽处理函数
    const handleDragStart = (e: React.DragEvent<HTMLDivElement>, index: number) => {
        setDraggedIndex(index);
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.currentTarget.outerHTML);
    };

    const handleDragEnd = () => {
        setDraggedIndex(null);
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>, dropIndex: number) => {
        e.preventDefault();

        if (draggedIndex === null || draggedIndex === dropIndex) return;

        const newCommandBlocks = [...commandBlocks];
        const draggedItem = newCommandBlocks[draggedIndex];

        newCommandBlocks.splice(draggedIndex, 1);
        const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
        newCommandBlocks.splice(insertIndex, 0, draggedItem);

        setCommandBlocks(newCommandBlocks);
        setDraggedIndex(null);
    };

    // 发送消息
    const handleSend = () => {
        if ((!input.trim() && commandBlocks.length === 0) || isGenerating) {
            return;
        }

        // 检测是否为代码执行请求
        const isCodeExecution = detectAndExecuteCode(input.trim());

        if (!isCodeExecution) {
            // 正常发送消息
            const validCommandBlocks = commandBlocks.filter(block =>
                block.type === 'FSD' || block.value.trim()
            );

            // 去重处理 - 确保不会有重复的命令块
            const uniqueCommandBlocks = validCommandBlocks.reduce<CommandBlock[]>((acc, current) => {
                const existing = acc.find(item =>
                    item.type === current.type &&
                    item.value === current.value
                );
                if (!existing) {
                    acc.push(current);
                }
                return acc;
            }, []);

            sendMessage(input.trim(), uniqueCommandBlocks);
        }

        setInput('');
        setCommandBlocks([]);
        setShowCommandPicker(false);
    };

    // 停止生成
    const handleStop = () => {
        stopGeneration();
    };

    // 获取命令块图标
    const getCommandIcon = (type: CommandBlock['type']) => {
        switch (type) {
            case 'TraceId':
                return <Hash className="h-3 w-3"/>;
            case 'UnionId':
                return <User className="h-3 w-3"/>;
            case 'FSD':
                return <Link className="h-3 w-3"/>;
            default:
                return <Hash className="h-3 w-3"/>;
        }
    };

    // 渲染命令块
    const renderCommandBlock = (block: CommandBlock, index: number) => {
        if (block.type === 'FSD') {
            // FSD 类型显示为链接卡片，移除重复的前缀
            const displayName = block.displayValue ? block.displayValue.replace(/^FSD:\s*/, '') : block.value;

            return (
                <Card
                    key={block.id}
                    className={
                        `relative cursor-move transition-all duration-200 ${draggedIndex === index ? 'opacity-50 scale-95' : 'hover:shadow-md'}`
                    }
                    draggable
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDragEnd={handleDragEnd}
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, index)}
                >
                    <CardContent className="p-2 sm:p-3">
                        <div className="flex items-center gap-2 sm:gap-3">
                            <GripVertical className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground cursor-grab"/>
                            <div className="flex items-center gap-1 sm:gap-2">
                                <Link className="h-3 w-3 sm:h-4 sm:w-4 text-primary"/>
                                <div className="flex-1 min-w-0">
                                    <div className="text-xs sm:text-sm font-medium truncate">
                                        {displayName}
                                    </div>
                                    <div className="text-xs text-muted-foreground flex items-center gap-1">
                                        <span>FSD 服务链路</span>
                                        <ExternalLink className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                    </div>
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                                onClick={() => removeCommandBlock(block.id)}
                            >
                                <X className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            );
        }

        // TraceId 和 UnionId 类型显示为输入框，移除重复的前缀
        return (
            <Card
                key={block.id}
                className={`
          relative cursor-move transition-all duration-200 
          ${draggedIndex === index ? 'opacity-50 scale-95' : ''}
        `}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
            >
                <CardContent className="p-2">
                    <div className="flex items-center gap-1 sm:gap-2">
                        <GripVertical className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground cursor-grab"/>
                        {getCommandIcon(block.type)}
                        <span className="text-xs sm:text-sm font-medium">{block.type}</span>
                        <input
                            type="text"
                            placeholder={`输入 ${block.type}`}
                            value={block.value}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateCommandBlock(block.id, e.target.value)}
                            className="bg-transparent border-none outline-none text-xs sm:text-sm min-w-[80px] sm:min-w-[120px] flex-1"/>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0"
                            onClick={() => removeCommandBlock(block.id)}
                        >
                            <X className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    };

    return (
        <div className="relative w-full h-full">
            {/* 命令选择器 */}
            {showCommandPicker && (
                <div className="absolute bottom-full left-0 right-0 mb-2 z-50">
                    <CommandPicker
                        onSelect={addCommandBlock}
                        onClose={() => setShowCommandPicker(false)}
                    />
                </div>
            )}

            <div className="w-full h-full p-2 sm:p-3 lg:p-4 space-y-2 sm:space-y-3">
                {/* 命令执行结果显示 - 响应式高度 */}
                {
                    commandResults.length > 0 && (
                        <div className="space-y-2 max-h-60 sm:max-h-80 lg:max-h-96 overflow-y-auto custom-scrollbar">
                            {commandResults.map((result) => (
                                <div key={result.id} className="relative">
                                    <CommandResult result={result}/>
                                    {result.status !== 'running' && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="absolute top-2 right-2 h-5 w-5 sm:h-6 sm:w-6 p-0"
                                            onClick={() => removeCommandResult(result.id)}
                                        >
                                            <X className="h-2.5 w-2.5 sm:h-3 sm:w-3"/>
                                        </Button>
                                    )}
                                </div>
                            ))}
                        </div>
                    )
                }

                {/* 命令块显示 - 响应式间距 */}
                {commandBlocks.length > 0 && (
                    <div className="space-y-1 sm:space-y-2">
                        {commandBlocks.map((block, index) => renderCommandBlock(block, index))}
                    </div>
                )}

                {/* 输入区域 - 响应式布局 */}
                <div className="flex gap-1 sm:gap-2 w-full">
                    <div className="flex-1 relative">
                        <Textarea
                            ref={textareaRef}
                            value={input}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            placeholder="输入消息或代码... (输入 / 可以快速添加命令，直接输入代码可执行)"
                            className="min-h-[50px] sm:min-h-[60px] lg:min-h-[70px] max-h-[150px] sm:max-h-[200px] resize-none pr-10 sm:pr-12 text-sm sm:text-base w-full"
                            disabled={isGenerating || isExecutingCommand}
                        />

                        {/* 附件按钮 - 响应式位置 */}
                        <Button
                            variant="ghost"
                            size="sm"
                            className="absolute right-1 sm:right-2 top-1 sm:top-2 h-6 w-6 sm:h-8 sm:w-8 p-0"
                            disabled={isGenerating || isExecutingCommand}
                        >
                            <Paperclip className="h-3 w-3 sm:h-4 sm:w-4"/>
                        </Button>
                    </div>

                    {/* 发送/停止按钮 - 响应式大小 */}
                    {isGenerating ? (
                        <Button
                            onClick={handleStop}
                            variant="outline"
                            size="sm"
                            className="px-2 sm:px-3 h-[50px] sm:h-[60px] lg:h-[70px] min-w-[50px] sm:min-w-[60px]"
                        >
                            <Square className="h-3 w-3 sm:h-4 sm:w-4"/>
                        </Button>
                    ) : (
                        <Button
                            onClick={handleSend}
                            disabled={(!input.trim() && commandBlocks.length === 0) || isExecutingCommand}
                            size="sm"
                            className="px-2 sm:px-3 h-[50px] sm:h-[60px] lg:h-[70px] min-w-[50px] sm:min-w-[60px]"
                        >
                            {isExecutingCommand ? (
                                <Terminal className="h-3 w-3 sm:h-4 sm:w-4 animate-pulse"/>
                            ) : (
                                <Send className="h-3 w-3 sm:h-4 sm:w-4"/>
                            )}
                        </Button>
                    )}
                </div>

                {/* 提示文本 - 响应式显示 */}
                <div className="text-xs text-muted-foreground">
                    <span className="hidden sm:inline">
                        按 Enter 发送，Shift + Enter 换行 • 拖拽命令块可以调整顺序 • 直接输入代码可立即执行
                    </span>
                    <span className="sm:hidden">
                        Enter 发送 • 拖拽调整顺序 • 可执行代码
                    </span>
                </div>
            </div>
        </div>
    );
};
