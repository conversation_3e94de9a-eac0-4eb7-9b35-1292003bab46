import * as React from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {Progress} from '@/components/ui/progress';
import {TraceTreeCompact} from '../trace/TraceTreeCompact';
import {AlertTriangle, CheckCircle, Database, Globe, Server, XCircle, Zap} from 'lucide-react';
import {TraceNode} from '@/service/types';

// 定义服务类型
type ServiceType = 'http' | 'db' | 'redis' | 'thrift';

// 定义状态类型
type StatusType = 'success' | 'error' | 'timeout';

// 定义本地节点数据类型（用于模拟数据）
interface LocalTraceNode {
    name: string;
    type: ServiceType;
    duration: number;
    status: StatusType;
}

// 定义组件 Props 类型
interface MessageContentProps {
    content: string;
    isStreaming?: boolean;
    toolResult?: TraceNode;
}

export const MessageContent: React.FC<MessageContentProps> = ({content, isStreaming, toolResult}) => {
    // 简单的 Markdown 渲染
    const renderMarkdown = (text: string): string => {
        // 代码块
        text = text.replace(/```(\w+)?\n([\s\S]*?)```/g, (_match: string, _lang: string, code: string) => {
            return `<div class="code-block"><pre><code>${code.trim()}</code></pre></div>`;
        });

        // 行内代码
        text = text.replace(/`([^`]+)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>');

        // 粗体
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // 斜体
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // 换行
        text = text.replace(/\n/g, '<br>');

        return text;
    };

    const getServiceIcon = (type: ServiceType): React.ReactElement => {
        switch (type) {
            case 'http':
                return <Globe className="h-4 w-4"/>;
            case 'db':
                return <Database className="h-4 w-4"/>;
            case 'redis':
                return <Server className="h-4 w-4"/>;
            case 'thrift':
                return <Zap className="h-4 w-4"/>;
            default:
                return <Server className="h-4 w-4"/>;
        }
    };


    // 检查是否包含链路数据
    const hasTraceData = content.includes('链路分析') || content.includes('调用链路') || toolResult;

    // 模拟链路数据
    const mockTraceNodes: LocalTraceNode[] = [
        {name: 'API Gateway', type: 'http', duration: 2300, status: 'success'},
        {name: 'User Service', type: 'http', duration: 150, status: 'success'},
        {name: 'Order Service', type: 'http', duration: 800, status: 'success'},
        {name: 'Payment Service', type: 'http', duration: 1200, status: 'success'},
        {name: 'MySQL Query', type: 'db', duration: 45, status: 'success'},
        {name: 'Redis Cache', type: 'redis', duration: 5, status: 'success'}
    ];

    return (
        <div className="space-y-3">
            {/* 主要内容 */}
            <div
                className="prose prose-sm max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{__html: renderMarkdown(content)}}
            />

            {/* 流式输入指示器 */}
            {isStreaming && (
                <div className="flex items-center gap-2 text-muted-foreground">
                    <div className="flex gap-1">
                        <div
                            className="w-2 h-2 bg-current rounded-full animate-pulse-dot"
                            style={{animationDelay: '0ms'}}
                        />
                        <div
                            className="w-2 h-2 bg-current rounded-full animate-pulse-dot"
                            style={{animationDelay: '150ms'}}
                        />
                        <div
                            className="w-2 h-2 bg-current rounded-full animate-pulse-dot"
                            style={{animationDelay: '300ms'}}
                        />
                    </div>
                    <span className="text-xs">正在生成...</span>
                </div>
            )}

            {/* Trace 链路数据展示 - 使用紧凑版组件 */}
            {toolResult && !isStreaming && (
                <TraceTreeCompact traceData={toolResult} className="mt-4"/>
            )}

            {/* 兼容旧版本的模拟链路数据展示 */}
            {hasTraceData && !toolResult && !isStreaming && (
                <Card className="mt-4">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                            <Server className="h-4 w-4"/>
                            调用链路分析
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        {/* 总体统计 */}
                        <div className="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div className="text-lg font-semibold">2.3s</div>
                                <div className="text-xs text-muted-foreground">总耗时</div>
                            </div>
                            <div>
                                <div className="text-lg font-semibold">8</div>
                                <div className="text-xs text-muted-foreground">服务调用</div>
                            </div>
                            <div>
                                <div className="text-lg font-semibold">3</div>
                                <div className="text-xs text-muted-foreground">数据库查询</div>
                            </div>
                        </div>

                        {/* 关键路径 */}
                        <div className="space-y-2">
                            <div className="text-sm font-medium">关键调用路径</div>
                            {mockTraceNodes.map((node: LocalTraceNode, index: number) => (
                                <div key={index} className="flex items-center gap-3 p-2 rounded-lg bg-muted/30">
                                    <div className={`trace-node-${node.type} p-1 rounded`}>
                                        {getServiceIcon(node.type)}
                                    </div>
                                    <div className="flex-1">
                                        <div className="text-sm font-medium">{node.name}</div>
                                        <div className="text-xs text-muted-foreground">
                                            {node.duration}ms
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        {node.status === 'success' ? (
                                            <CheckCircle className="h-3 w-3 text-status-success"/>
                                        ) : node.status === 'error' ? (
                                            <XCircle className="h-3 w-3 text-status-error"/>
                                        ) : (
                                            <AlertTriangle className="h-3 w-3 text-status-warning"/>
                                        )}
                                        <Badge variant="outline" className="text-xs">
                                            {node.type.toUpperCase()}
                                        </Badge>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* 性能指标 */}
                        <div className="space-y-2">
                            <div className="text-sm font-medium">性能指标</div>
                            <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm">缓存命中率</span>
                                    <span className="text-sm font-medium">85%</span>
                                </div>
                                <Progress value={85} className="h-2"/>

                                <div className="flex justify-between items-center">
                                    <span className="text-sm">数据库性能</span>
                                    <span className="text-sm font-medium">92%</span>
                                </div>
                                <Progress value={92} className="h-2"/>

                                <div className="flex justify-between items-center">
                                    <span className="text-sm">服务可用性</span>
                                    <span className="text-sm font-medium">99.9%</span>
                                </div>
                                <Progress value={99.9} className="h-2"/>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
};
