import * as React from 'react';
import {useChatStore} from '@/store/chatStore.ts';
import {Button} from '@/components/ui/button';
import {ScrollArea} from '@/components/ui/scroll-area';
import {MessageSquare, MoreHorizontal, PanelLeftClose, Plus, Share, Trash2, X} from 'lucide-react';
import {UserMenu} from './UserMenu';
import {Conversation} from '@/service/types';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu.tsx";

// 定义分组会话类型
interface GroupedConversations {
    [dateKey: string]: Conversation[];
}

export const Sidebar: React.FC = () => {
    const {
        conversations,
        currentConversation,
        createNewConversation,
        selectConversation,
        deleteConversation,
        sidebarCollapsed,
        setSidebarCollapsed
    } = useChatStore();

    if (sidebarCollapsed) return null;

    const formatDate = (date: Date): string => {
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (days === 0) return '今天';
        if (days === 1) return '昨天';
        if (days < 7) return `${days}天前`;
        if (days < 30) return `${Math.floor(days / 7)}周前`;
        return `${Math.floor(days / 30)}个月前`;
    };

    const groupedConversations: GroupedConversations = conversations.reduce<GroupedConversations>((groups, conv) => {
        const dateKey = formatDate(conv.updatedAt);
        if (!groups[dateKey]) {
            groups[dateKey] = [];
        }
        groups[dateKey].push(conv);
        return groups;
    }, {});

    const handleConversationSelect = (conversationId: string): void => {
        selectConversation(conversationId);
        // 在小屏幕上选择对话后自动收起侧边栏
        if (window.innerWidth < 640) { // sm breakpoint
            setSidebarCollapsed(true);
        }
    };

    const handleShareClick = (e: React.MouseEvent<HTMLButtonElement>): void => {
        e.stopPropagation();
        // TODO: 实现分享功能
    };

    const handleDeleteClick = (e: React.MouseEvent<HTMLButtonElement>, conversationId: string): void => {
        e.stopPropagation();
        deleteConversation(conversationId);
    };

    return (
        <div className="h-full flex flex-col relative z-50 sm:z-auto bg-card">
            {/* 顶部操作区 - 响应式间距和移动端关闭按钮 */}
            <div className="p-3 sm:p-3 border-b border-border flex-shrink-0">
                <div className="flex items-center gap-2">
                    {/* 移动端关闭按钮 */}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSidebarCollapsed(true)}
                        className="h-8 w-8 p-0 sm:hidden flex-shrink-0"
                    >
                        <X className="h-4 w-4"/>
                    </Button>

                    <Button
                        onClick={createNewConversation}
                        className="flex-1 justify-start h-9 sm:h-10 text-sm px-3"
                        variant="outline"
                    >
                        <Plus className="h-4 w-4 mr-2"/>
                        新建对话
                    </Button>

                    {/* 桌面端收起按钮 */}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSidebarCollapsed(true)}
                        className="h-9 w-9 p-0 hidden sm:flex flex-shrink-0"
                    >
                        <PanelLeftClose className="h-4 w-4"/>
                    </Button>
                </div>
            </div>

            {/* 会话列表 - 占据剩余空间并可滚动 */}
            <div className="flex-1 overflow-hidden">
                <ScrollArea className="h-full custom-scrollbar">
                    <div className="p-2 sm:p-3">
                        {Object.entries(groupedConversations).map(([dateKey, convs]: [string, Conversation[]]) => (
                            <div key={dateKey} className="mb-3 sm:mb-4">
                                <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                                    {dateKey}
                                </div>
                                <div className="space-y-1">
                                    {convs.map((conversation: Conversation) => (
                                        <div
                                            key={conversation.id}
                                            className={`group relative rounded-lg cursor-pointer transition-colors hover:bg-accent active:bg-accent/80
                                                 ${currentConversation?.id === conversation.id
                                                ? 'bg-accent text-accent-foreground'
                                                : ''
                                            }`}
                                            onClick={() => handleConversationSelect(conversation.id)}
                                        >
                                            <div className="grid grid-cols-[auto_1fr_auto] items-center gap-2 p-3">
                                                <MessageSquare className="h-4 w-4"/>
                                                <div className="min-w-0 overflow-hidden">
                                                    <div className="text-sm font-medium leading-tight truncate">
                                                        {conversation.title}
                                                    </div>
                                                    <div className="text-xs text-muted-foreground mt-0.5 truncate">
                                                        {conversation.messages.length} 条消息
                                                    </div>
                                                </div>

                                                {/* 操作菜单 - 三点菜单 */}
                                                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-6 w-6 p-0 hover:bg-accent"
                                                                onClick={(e) => e.stopPropagation()}
                                                            >
                                                                <MoreHorizontal className="h-4 w-4"/>
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="start" className="w-30">
                                                            <DropdownMenuItem
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleShareClick(e as any);
                                                                }}
                                                                className="cursor-pointer"
                                                            >
                                                                <Share className="h-4 w-4 mr-2"/>
                                                                分享对话
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleDeleteClick(e as any, conversation.id);
                                                                }}
                                                                className="cursor-pointer text-destructive focus:text-destructive"
                                                            >
                                                                <Trash2 className="h-4 w-4 mr-2"/>
                                                                删除对话
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}

                        {conversations.length === 0 && (
                            <div className="text-center text-muted-foreground py-6 sm:py-8">
                                <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50"/>
                                <p className="text-sm">暂无对话记录</p>
                                <p className="text-xs mt-1">点击上方按钮开始新对话</p>
                            </div>
                        )}
                    </div>
                </ScrollArea>
            </div>

            {/* 底部用户区 - 固定在底部，响应式布局 */}
            <div className="p-3 sm:p-4 border-t border-border flex-shrink-0">
                <UserMenu/>
            </div>
        </div>
    );
};
