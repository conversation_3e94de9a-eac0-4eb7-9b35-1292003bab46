import * as React from 'react';
import {useCallback, useEffect, useState} from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {ScrollArea} from '@/components/ui/scroll-area';
import {Badge} from '@/components/ui/badge';
import {ExternalLink, Hash, Link, Search, Settings, User} from 'lucide-react';
import {apiService} from '@/service';
import {useChatStore} from '@/store/chatStore.ts';
import {useNavigate} from 'react-router-dom';

// 定义命令类型
interface Command {
    type: string;
    label: string;
    description?: string;
    icon: React.ComponentType<{ className?: string }>;
    example?: string;
    value?: string;
    displayValue?: string;
    unionIdData?: UnionIdItem;
    linkData?: FSDLink;
}

// 定义 UnionId 项类型
interface UnionIdItem {
    id: string;
    name: string;
    value: string;
    description?: string;
}

// 定义 FSD 链接类型
interface FSDLink {
    id: string;
    name: string;
    url: string;
    description?: string;
}

// 定义 Tab 类型
type TabType = 'commands' | 'unionid' | 'fsd';

// 定义组件 Props 类型
interface CommandPickerProps {
    onSelect: (command: Command) => void;
    onClose: () => void;
}

export const CommandPicker: React.FC<CommandPickerProps> = ({onSelect, onClose}) => {
    const navigate = useNavigate();
    const {userConfig} = useChatStore();
    const [activeTab, setActiveTab] = useState<TabType>('commands');
    const [fsdLinks, setFsdLinks] = useState<FSDLink[]>([]);
    const [fsdQuery, setFsdQuery] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const commands: Command[] = [
        {
            type: 'TraceId',
            label: 'TraceId 分析',
            description: '分析调用链路，识别性能瓶颈和异常',
            icon: Hash,
            example: 'trace_12345678'
        }
    ];

    // 从用户配置获取 UnionId 列表
    const savedUnionIds: UnionIdItem[] = userConfig.unionIds || [];

    // 加载 FSD 链接
    const loadFSDLinks = useCallback(async () => {
        setIsLoading(true);
        try {
            const links = await apiService.getFSDLinks(fsdQuery);
            setFsdLinks(links);
        } catch (error) {
            console.error('加载 FSD 链接失败:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fsdQuery]);

    useEffect(() => {
        if (activeTab === 'fsd') {
            loadFSDLinks();
        }
    }, [activeTab, loadFSDLinks]);

    const handleCommandSelect = (command: Command) => {
        onSelect(command);
    };

    const handleUnionIdSelect = (unionIdItem: UnionIdItem) => {
        onSelect({
            type: 'UnionId',
            label: 'UnionId 查询',
            value: unionIdItem.value,
            displayValue: `UnionId: ${unionIdItem.name || unionIdItem.value}`,
            unionIdData: unionIdItem,
            icon: User
        });
    };

    const handleFSDSelect = (link: FSDLink) => {
        onSelect({
            type: 'FSD',
            label: 'FSD 链路',
            value: link.url,
            displayValue: `FSD: ${link.name}`,
            linkData: {
                id: link.id,
                name: link.name,
                url: link.url,
                description: link.description
            },
            icon: Link
        });
    };

    const handleGoToSettings = () => {
        onClose();
        navigate('/settings');
    };

    const handleFsdQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFsdQuery(e.target.value);
    };

    return (
        <Card className="w-full max-w-md mx-auto shadow-lg">
            <CardHeader className="pb-2">
                <CardTitle className="text-sm">选择命令类型</CardTitle>
                <div className="flex gap-1">
                    <Button
                        variant={activeTab === 'commands' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setActiveTab('commands')}
                    >
                        命令
                    </Button>
                    <Button
                        variant={activeTab === 'unionid' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setActiveTab('unionid')}
                    >
                        UnionId
                    </Button>
                    <Button
                        variant={activeTab === 'fsd' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setActiveTab('fsd')}
                    >
                        FSD 链路
                    </Button>
                </div>
            </CardHeader>

            <CardContent className="p-0">
                {activeTab === 'commands' && (
                    <ScrollArea className="max-h-60">
                        <div className="p-3 space-y-2">
                            {commands.map((command) => (
                                <Button
                                    key={command.type}
                                    variant="ghost"
                                    className="w-full justify-start h-auto p-3"
                                    onClick={() => handleCommandSelect(command)}
                                >
                                    <div className="flex items-start gap-3">
                                        <command.icon className="h-4 w-4 mt-0.5 flex-shrink-0"/>
                                        <div className="text-left">
                                            <div className="font-medium">{command.label}</div>
                                            {command.description && (
                                                <div className="text-xs text-muted-foreground">
                                                    {command.description}
                                                </div>
                                            )}
                                            {command.example && (
                                                <Badge variant="outline" className="text-xs mt-1">
                                                    示例: {command.example}
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                </Button>
                            ))}
                        </div>
                    </ScrollArea>
                )}

                {activeTab === 'unionid' && (
                    <div className="space-y-3">
                        {/* 管理按钮 */}
                        <div className="p-3 pb-0">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleGoToSettings}
                                className="w-full"
                            >
                                <Settings className="h-4 w-4 mr-2"/>
                                管理 UnionId
                            </Button>
                        </div>

                        {/* UnionId 列表 */}
                        <ScrollArea className="max-h-60">
                            <div className="p-3 pt-0 space-y-2">
                                {savedUnionIds.length > 0 ? (
                                    savedUnionIds.map((unionIdItem) => (
                                        <Button
                                            key={unionIdItem.id}
                                            variant="ghost"
                                            className="w-full justify-start h-auto p-3 hover:bg-accent"
                                            onClick={() => handleUnionIdSelect(unionIdItem)}
                                        >
                                            <div className="flex items-start gap-3 w-full">
                                                <User className="h-4 w-4 mt-0.5 flex-shrink-0 text-primary"/>
                                                <div className="text-left flex-1">
                                                    <div className="font-medium">{unionIdItem.name}</div>
                                                    <div className="text-xs text-muted-foreground font-mono">
                                                        {unionIdItem.value}
                                                    </div>
                                                    {unionIdItem.description && (
                                                        <div className="text-xs text-muted-foreground mt-1">
                                                            {unionIdItem.description}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </Button>
                                    ))
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        <User className="h-8 w-8 mx-auto mb-2 opacity-50"/>
                                        <p className="text-sm">暂无保存的 UnionId</p>
                                        <Button
                                            variant="link"
                                            size="sm"
                                            onClick={handleGoToSettings}
                                            className="mt-2"
                                        >
                                            去添加 UnionId
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </ScrollArea>
                    </div>
                )}

                {activeTab === 'fsd' && (
                    <div className="space-y-3">
                        {/* 搜索框 */}
                        <div className="p-3 pb-0">
                            <div className="relative">
                                <Search
                                    className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"/>
                                <Input
                                    placeholder="搜索 FSD 链路..."
                                    value={fsdQuery}
                                    onChange={handleFsdQueryChange}
                                    className="pl-10"
                                />
                            </div>
                        </div>

                        {/* FSD 链接列表 */}
                        <ScrollArea className="max-h-60">
                            <div className="p-3 pt-0 space-y-2">
                                {isLoading ? (
                                    <div className="text-center text-muted-foreground py-4">
                                        加载中...
                                    </div>
                                ) : fsdLinks.length > 0 ? (
                                    fsdLinks.map((link) => (
                                        <Button
                                            key={link.id}
                                            variant="ghost"
                                            className="w-full justify-start h-auto p-3 hover:bg-accent"
                                            onClick={() => handleFSDSelect(link)}
                                        >
                                            <div className="flex items-start gap-3 w-full">
                                                <Link className="h-4 w-4 mt-0.5 flex-shrink-0 text-primary"/>
                                                <div className="text-left flex-1">
                                                    <div className="font-medium">{link.name}</div>
                                                    {link.description && (
                                                        <div className="text-xs text-muted-foreground">
                                                            {link.description}
                                                        </div>
                                                    )}
                                                    <div
                                                        className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                                                        <span>点击直接添加到消息</span>
                                                        <ExternalLink className="h-3 w-3"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </Button>
                                    ))
                                ) : (
                                    <div className="text-center text-muted-foreground py-4">
                                        {fsdQuery ? '未找到匹配的链路' : '暂无可用链路'}
                                    </div>
                                )}
                            </div>
                        </ScrollArea>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
