import React, {useState} from 'react';
import {Card, CardContent} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs';
import {ArrowLeft, BarChart3, Settings, Shield, Users} from 'lucide-react';
import {useNavigate} from 'react-router-dom';
import {UnionIdManager} from './UnionIdManager';
import {SystemSettings} from './SystemSettings';
import {UserAnalytics} from './UserAnalytics';
import {SecurityManager} from './SecurityManager';

export const ManagementDashboard = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('unionid');

  const managementModules = [
    {
      id: 'unionid',
      title: 'UnionId 管理',
      description: '管理用户 UnionId，批量导入导出',
      icon: Users,
      component: UnionIdManager
    },
    {
      id: 'analytics',
      title: '用户分析',
      description: '查看用户行为数据和使用统计',
      icon: BarChart3,
      component: UserAnalytics
    },
    {
      id: 'security',
      title: '安全管理',
      description: '权限控制和安全策略配置',
      icon: Shield,
      component: SecurityManager
    },
    {
      id: 'system',
      title: '系统设置',
      description: '全局配置和系统参数管理',
      icon: Settings,
      component: SystemSettings
    }
  ];

  return (
    <div className="h-full flex flex-col">
      {/* 顶部导航 */}
      <div className="h-14 border-b border-border bg-card/50 backdrop-blur-sm flex items-center px-4 flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/')}
          className="mr-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回聊天
        </Button>
        
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          <h1 className="text-lg font-semibold">管理中心</h1>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 overflow-hidden p-6">
        <div className="max-w-7xl mx-auto h-full">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            {/* 标签页导航 */}
            <TabsList className="grid w-full grid-cols-4 mb-6">
              {managementModules.map((module) => (
                <TabsTrigger 
                  key={module.id} 
                  value={module.id}
                  className="flex items-center gap-2"
                >
                  <module.icon className="h-4 w-4" />
                  {module.title}
                </TabsTrigger>
              ))}
            </TabsList>

            {/* 标签页内容 */}
            <div className="flex-1 overflow-hidden">
              {managementModules.map((module) => (
                <TabsContent 
                  key={module.id} 
                  value={module.id} 
                  className="h-full mt-0"
                >
                  <div className="h-full flex flex-col">
                    {/* 模块描述 */}
                    <Card className="mb-4">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-primary/10 rounded-lg">
                            <module.icon className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <h2 className="font-semibold">{module.title}</h2>
                            <p className="text-sm text-muted-foreground">
                              {module.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* 模块内容 */}
                    <div className="flex-1 overflow-hidden">
                      <module.component />
                    </div>
                  </div>
                </TabsContent>
              ))}
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
