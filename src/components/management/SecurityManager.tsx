import React, {useState} from 'react';
import {<PERSON>, Card<PERSON>ontent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Switch} from '@/components/ui/switch';
import {Badge} from '@/components/ui/badge';
import {ScrollArea} from '@/components/ui/scroll-area';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs';
import {AlertTriangle, CheckCircle, Eye, EyeOff, Key, Plus, Shield, Trash2, XCircle} from 'lucide-react';

export const SecurityManager = () => {
  const [securitySettings, setSecuritySettings] = useState({
    enableAuth: true,
    enableRateLimit: true,
    enableAuditLog: true,
    maxSessionDuration: 24,
    maxFailedAttempts: 5,
    enableIPWhitelist: false
  });

  const [apiKeys, setApiKeys] = useState([
    {
      id: '1',
      name: 'Production API Key',
      key: 'sk-prod-1234567890abcdef',
      permissions: ['read', 'write'],
      lastUsed: new Date(),
      isActive: true
    },
    {
      id: '2',
      name: 'Development API Key',
      key: 'sk-dev-abcdef1234567890',
      permissions: ['read'],
      lastUsed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      isActive: true
    }
  ]);

  const [auditLogs] = useState([
    {
      id: '1',
      action: '用户登录',
      user: '<EMAIL>',
      ip: '*************',
      timestamp: new Date(),
      status: 'success'
    },
    {
      id: '2',
      action: 'API 调用',
      user: 'system',
      ip: '********',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      status: 'success'
    },
    {
      id: '3',
      action: '登录失败',
      user: '<EMAIL>',
      ip: '***********',
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      status: 'failed'
    }
  ]);

  const [showApiKey, setShowApiKey] = useState({});

  const handleSettingChange = (key, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const toggleApiKeyVisibility = (keyId) => {
    setShowApiKey(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const generateApiKey = () => {
    const newKey = {
      id: Date.now().toString(),
      name: `API Key ${apiKeys.length + 1}`,
      key: `sk-${Math.random().toString(36).substr(2, 20)}`,
      permissions: ['read'],
      lastUsed: null,
      isActive: true
    };
    setApiKeys(prev => [...prev, newKey]);
  };

  const revokeApiKey = (keyId) => {
    setApiKeys(prev => prev.filter(key => key.id !== keyId));
  };

  const maskApiKey = (key) => {
    return key.substring(0, 8) + '...' + key.substring(key.length - 4);
  };

  return (
    <div className="h-full">
      <Tabs defaultValue="settings" className="h-full flex flex-col">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="settings">安全设置</TabsTrigger>
          <TabsTrigger value="apikeys">API 密钥</TabsTrigger>
          <TabsTrigger value="audit">审计日志</TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-hidden mt-4">
          <TabsContent value="settings" className="h-full mt-0">
            <div className="grid grid-cols-2 gap-6 h-full">
              {/* 基础安全设置 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    基础安全设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>启用身份验证</Label>
                      <p className="text-sm text-muted-foreground">要求用户登录才能访问系统</p>
                    </div>
                    <Switch
                      checked={securitySettings.enableAuth}
                      onCheckedChange={(checked) => handleSettingChange('enableAuth', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>启用速率限制</Label>
                      <p className="text-sm text-muted-foreground">限制 API 调用频率</p>
                    </div>
                    <Switch
                      checked={securitySettings.enableRateLimit}
                      onCheckedChange={(checked) => handleSettingChange('enableRateLimit', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>启用审计日志</Label>
                      <p className="text-sm text-muted-foreground">记录所有用户操作</p>
                    </div>
                    <Switch
                      checked={securitySettings.enableAuditLog}
                      onCheckedChange={(checked) => handleSettingChange('enableAuditLog', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>启用 IP 白名单</Label>
                      <p className="text-sm text-muted-foreground">只允许特定 IP 访问</p>
                    </div>
                    <Switch
                      checked={securitySettings.enableIPWhitelist}
                      onCheckedChange={(checked) => handleSettingChange('enableIPWhitelist', checked)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>会话超时时间（小时）</Label>
                    <Input
                      type="number"
                      value={securitySettings.maxSessionDuration}
                      onChange={(e) => handleSettingChange('maxSessionDuration', parseInt(e.target.value))}
                      min="1"
                      max="168"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>最大登录失败次数</Label>
                    <Input
                      type="number"
                      value={securitySettings.maxFailedAttempts}
                      onChange={(e) => handleSettingChange('maxFailedAttempts', parseInt(e.target.value))}
                      min="1"
                      max="10"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* 安全状态监控 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    安全状态监控
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium">系统安全</span>
                      </div>
                      <p className="text-2xl font-bold text-green-600">正常</p>
                    </div>

                    <div className="p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium">威胁检测</span>
                      </div>
                      <p className="text-2xl font-bold text-yellow-600">2 个警告</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">最近安全事件</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                        <div className="flex items-center gap-2">
                          <XCircle className="h-4 w-4 text-red-600" />
                          <span className="text-sm">多次登录失败</span>
                        </div>
                        <Badge variant="destructive">高风险</Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                          <span className="text-sm">异常 IP 访问</span>
                        </div>
                        <Badge variant="secondary">中风险</Badge>
                      </div>
                    </div>
                  </div>

                  <Button className="w-full">
                    查看详细报告
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="apikeys" className="h-full mt-0">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Key className="h-5 w-5" />
                    API 密钥管理
                  </div>
                  <Button onClick={generateApiKey}>
                    <Plus className="h-4 w-4 mr-2" />
                    生成新密钥
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-4">
                    {apiKeys.map((apiKey) => (
                      <Card key={apiKey.id}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <h3 className="font-medium">{apiKey.name}</h3>
                                <Badge variant={apiKey.isActive ? 'default' : 'secondary'}>
                                  {apiKey.isActive ? '活跃' : '已禁用'}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-2">
                                <code className="text-sm bg-muted px-2 py-1 rounded">
                                  {showApiKey[apiKey.id] ? apiKey.key : maskApiKey(apiKey.key)}
                                </code>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleApiKeyVisibility(apiKey.id)}
                                  className="h-6 w-6 p-0"
                                >
                                  {showApiKey[apiKey.id] ? (
                                    <EyeOff className="h-3 w-3" />
                                  ) : (
                                    <Eye className="h-3 w-3" />
                                  )}
                                </Button>
                              </div>
                              <div className="flex gap-2">
                                {apiKey.permissions.map((permission) => (
                                  <Badge key={permission} variant="outline" className="text-xs">
                                    {permission}
                                  </Badge>
                                ))}
                              </div>
                              <p className="text-xs text-muted-foreground">
                                最后使用: {apiKey.lastUsed ? apiKey.lastUsed.toLocaleString() : '从未使用'}
                              </p>
                            </div>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => revokeApiKey(apiKey.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audit" className="h-full mt-0">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  审计日志
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-3">
                    {auditLogs.map((log) => (
                      <div key={log.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${
                            log.status === 'success' ? 'bg-green-600' : 'bg-red-600'
                          }`} />
                          <div>
                            <p className="font-medium">{log.action}</p>
                            <p className="text-sm text-muted-foreground">
                              用户: {log.user} | IP: {log.ip}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant={log.status === 'success' ? 'default' : 'destructive'}>
                            {log.status === 'success' ? '成功' : '失败'}
                          </Badge>
                          <p className="text-xs text-muted-foreground mt-1">
                            {log.timestamp.toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};
