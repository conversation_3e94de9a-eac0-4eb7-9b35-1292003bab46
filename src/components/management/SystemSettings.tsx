import React, {useState} from 'react';
import {<PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Switch} from '@/components/ui/switch';
import {Textarea} from '@/components/ui/textarea';
import {Badge} from '@/components/ui/badge';
import {Ta<PERSON>, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs';
import {
    AlertTriangle,
    Bell,
    CheckCircle,
    Database,
    Globe,
    Info,
    Mail,
    RefreshCw,
    Save,
    Settings,
    Smartphone
} from 'lucide-react';

// 类型定义
interface SystemConfig {
    // 基础设置
    systemName: string;
    systemVersion: string;
    maxConcurrentUsers: number;
    sessionTimeout: number;
    enableDebugMode: boolean;
    enableMetrics: boolean;

    // 数据库设置
    dbHost: string;
    dbPort: number;
    dbName: string;
    dbPoolSize: number;
    dbTimeout: number;

    // API 设置
    apiRateLimit: number;
    apiTimeout: number;
    enableCors: boolean;
    allowedOrigins: string;

    // 通知设置
    enableEmailNotifications: boolean;
    enableSmsNotifications: boolean;
    emailSmtpHost: string;
    emailSmtpPort: number;
    emailUsername: string;
    emailPassword: string;

    // 缓存设置
    enableRedisCache: boolean;
    redisHost: string;
    redisPort: number;
    cacheExpiration: number;

    // 日志设置
    logLevel: LogLevel;
    logRetentionDays: number;
    enableFileLogging: boolean;
    enableConsoleLogging: boolean;
}

type LogLevel = 'debug' | 'info' | 'warn' | 'error';
type SaveStatus = 'success' | 'error' | null;

export const SystemSettings: React.FC = () => {
    const [systemConfig, setSystemConfig] = useState<SystemConfig>({
        // 基础设置
        systemName: '端到端测试智能体平台',
        systemVersion: '1.0.0',
        maxConcurrentUsers: 1000,
        sessionTimeout: 30,
        enableDebugMode: false,
        enableMetrics: true,

        // 数据库设置
        dbHost: 'localhost',
        dbPort: 5432,
        dbName: 'chatbot_db',
        dbPoolSize: 20,
        dbTimeout: 30,

        // API 设置
        apiRateLimit: 100,
        apiTimeout: 30,
        enableCors: true,
        allowedOrigins: '*',

        // 通知设置
        enableEmailNotifications: true,
        enableSmsNotifications: false,
        emailSmtpHost: 'smtp.example.com',
        emailSmtpPort: 587,
        emailUsername: '',
        emailPassword: '',

        // 缓存设置
        enableRedisCache: true,
        redisHost: 'localhost',
        redisPort: 6379,
        cacheExpiration: 3600,

        // 日志设置
        logLevel: 'info',
        logRetentionDays: 30,
        enableFileLogging: true,
        enableConsoleLogging: true
    });

    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [saveStatus, setSaveStatus] = useState<SaveStatus>(null);

    const handleConfigChange = <K extends keyof SystemConfig>(
        key: K,
        value: SystemConfig[K]
    ): void => {
        setSystemConfig(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handleSave = async (): Promise<void> => {
        setIsSaving(true);
        setSaveStatus(null);

        try {
            // 模拟保存配置
            await new Promise<void>(resolve => setTimeout(resolve, 1500));

            // 这里应该调用实际的 API 保存配置
            console.log('保存系统配置:', systemConfig);

            setSaveStatus('success');
            setTimeout(() => setSaveStatus(null), 3000);
        } catch (error) {
            console.error('保存配置失败:', error);
            setSaveStatus('error');
            setTimeout(() => setSaveStatus(null), 3000);
        } finally {
            setIsSaving(false);
        }
    };

    const handleReset = (): void => {
        // 重置为默认配置
        setSystemConfig({
            systemName: '端到端测试智能体平台',
            systemVersion: '1.0.0',
            maxConcurrentUsers: 1000,
            sessionTimeout: 30,
            enableDebugMode: false,
            enableMetrics: true,
            dbHost: 'localhost',
            dbPort: 5432,
            dbName: 'chatbot_db',
            dbPoolSize: 20,
            dbTimeout: 30,
            apiRateLimit: 100,
            apiTimeout: 30,
            enableCors: true,
            allowedOrigins: '*',
            enableEmailNotifications: true,
            enableSmsNotifications: false,
            emailSmtpHost: 'smtp.example.com',
            emailSmtpPort: 587,
            emailUsername: '',
            emailPassword: '',
            enableRedisCache: true,
            redisHost: 'localhost',
            redisPort: 6379,
            cacheExpiration: 3600,
            logLevel: 'info',
            logRetentionDays: 30,
            enableFileLogging: true,
            enableConsoleLogging: true
        });
    };

    const getSaveStatusIcon = (): React.ReactNode => {
        switch (saveStatus) {
            case 'success':
                return <CheckCircle className="h-4 w-4 text-status-success"/>;
            case 'error':
                return <AlertTriangle className="h-4 w-4 text-status-error"/>;
            default:
                return null;
        }
    };

    // 事件处理函数
    const handleStringInputChange = (key: keyof SystemConfig) =>
        (e: React.ChangeEvent<HTMLInputElement>): void => {
            handleConfigChange(key, e.target.value);
        };

    const handleNumberInputChange = (key: keyof SystemConfig) =>
        (e: React.ChangeEvent<HTMLInputElement>): void => {
            const value = parseInt(e.target.value, 10);
            if (!isNaN(value)) {
                handleConfigChange(key, value as SystemConfig[typeof key]);
            }
        };

    const handleTextareaChange = (key: keyof SystemConfig) =>
        (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
            handleConfigChange(key, e.target.value);
        };

    const handleSelectChange = (key: keyof SystemConfig) =>
        (e: React.ChangeEvent<HTMLSelectElement>): void => {
            handleConfigChange(key, e.target.value as SystemConfig[typeof key]);
        };

    const handleSwitchChange = (key: keyof SystemConfig) =>
        (checked: boolean): void => {
            handleConfigChange(key, checked as SystemConfig[typeof key]);
        };

    return (
        <div className="h-full">
            <Tabs defaultValue="basic" className="h-full flex flex-col">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">基础设置</TabsTrigger>
                    <TabsTrigger value="database">数据库</TabsTrigger>
                    <TabsTrigger value="api">API 配置</TabsTrigger>
                    <TabsTrigger value="notifications">通知设置</TabsTrigger>
                </TabsList>

                <div className="flex-1 overflow-hidden mt-4">
                    <TabsContent value="basic" className="h-full mt-0">
                        <div className="grid grid-cols-2 gap-6 h-full">
                            {/* 系统基础设置 */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5"/>
                                        系统基础设置
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label>系统名称</Label>
                                        <Input
                                            value={systemConfig.systemName}
                                            onChange={handleStringInputChange('systemName')}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>系统版本</Label>
                                        <Input
                                            value={systemConfig.systemVersion}
                                            onChange={handleStringInputChange('systemVersion')}
                                            disabled
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>最大并发用户数</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.maxConcurrentUsers}
                                            onChange={handleNumberInputChange('maxConcurrentUsers')}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>会话超时时间（分钟）</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.sessionTimeout}
                                            onChange={handleNumberInputChange('sessionTimeout')}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label>启用调试模式</Label>
                                            <p className="text-sm text-muted-foreground">开启后会输出详细的调试信息</p>
                                        </div>
                                        <Switch
                                            checked={systemConfig.enableDebugMode}
                                            onCheckedChange={handleSwitchChange('enableDebugMode')}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label>启用性能监控</Label>
                                            <p className="text-sm text-muted-foreground">收集系统性能指标</p>
                                        </div>
                                        <Switch
                                            checked={systemConfig.enableMetrics}
                                            onCheckedChange={handleSwitchChange('enableMetrics')}
                                        />
                                    </div>
                                </CardContent>
                            </Card>

                            {/* 日志设置 */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Info className="h-5 w-5"/>
                                        日志设置
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label>日志级别</Label>
                                        <select
                                            value={systemConfig.logLevel}
                                            onChange={handleSelectChange('logLevel')}
                                            className="w-full p-2 border rounded-md"
                                        >
                                            <option value="debug">Debug</option>
                                            <option value="info">Info</option>
                                            <option value="warn">Warning</option>
                                            <option value="error">Error</option>
                                        </select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label>日志保留天数</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.logRetentionDays}
                                            onChange={handleNumberInputChange('logRetentionDays')}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label>启用文件日志</Label>
                                            <p className="text-sm text-muted-foreground">将日志写入文件</p>
                                        </div>
                                        <Switch
                                            checked={systemConfig.enableFileLogging}
                                            onCheckedChange={handleSwitchChange('enableFileLogging')}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label>启用控制台日志</Label>
                                            <p className="text-sm text-muted-foreground">在控制台输出日志</p>
                                        </div>
                                        <Switch
                                            checked={systemConfig.enableConsoleLogging}
                                            onCheckedChange={handleSwitchChange('enableConsoleLogging')}
                                        />
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="database" className="h-full mt-0">
                        <Card className="h-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Database className="h-5 w-5"/>
                                    数据库配置
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label>数据库主机</Label>
                                        <Input
                                            value={systemConfig.dbHost}
                                            onChange={handleStringInputChange('dbHost')}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>端口</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.dbPort}
                                            onChange={handleNumberInputChange('dbPort')}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>数据库名称</Label>
                                        <Input
                                            value={systemConfig.dbName}
                                            onChange={handleStringInputChange('dbName')}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>连接池大小</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.dbPoolSize}
                                            onChange={handleNumberInputChange('dbPoolSize')}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>连接超时（秒）</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.dbTimeout}
                                            onChange={handleNumberInputChange('dbTimeout')}
                                        />
                                    </div>
                                </div>

                                {/* Redis 缓存设置 */}
                                <div className="mt-6 space-y-4">
                                    <h3 className="text-lg font-semibold">Redis 缓存设置</h3>

                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label>启用 Redis 缓存</Label>
                                            <p className="text-sm text-muted-foreground">使用 Redis 进行数据缓存</p>
                                        </div>
                                        <Switch
                                            checked={systemConfig.enableRedisCache}
                                            onCheckedChange={handleSwitchChange('enableRedisCache')}
                                        />
                                    </div>

                                    {systemConfig.enableRedisCache && (
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label>Redis 主机</Label>
                                                <Input
                                                    value={systemConfig.redisHost}
                                                    onChange={handleStringInputChange('redisHost')}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label>Redis 端口</Label>
                                                <Input
                                                    type="number"
                                                    value={systemConfig.redisPort}
                                                    onChange={handleNumberInputChange('redisPort')}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label>缓存过期时间（秒）</Label>
                                                <Input
                                                    type="number"
                                                    value={systemConfig.cacheExpiration}
                                                    onChange={handleNumberInputChange('cacheExpiration')}
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="api" className="h-full mt-0">
                        <Card className="h-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Globe className="h-5 w-5"/>
                                    API 配置
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label>API 速率限制（请求/分钟）</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.apiRateLimit}
                                            onChange={handleNumberInputChange('apiRateLimit')}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label>API 超时时间（秒）</Label>
                                        <Input
                                            type="number"
                                            value={systemConfig.apiTimeout}
                                            onChange={handleNumberInputChange('apiTimeout')}
                                        />
                                    </div>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label>启用 CORS</Label>
                                        <p className="text-sm text-muted-foreground">允许跨域请求</p>
                                    </div>
                                    <Switch
                                        checked={systemConfig.enableCors}
                                        onCheckedChange={handleSwitchChange('enableCors')}
                                    />
                                </div>

                                {systemConfig.enableCors && (
                                    <div className="space-y-2">
                                        <Label>允许的来源</Label>
                                        <Textarea
                                            value={systemConfig.allowedOrigins}
                                            onChange={handleTextareaChange('allowedOrigins')}
                                            placeholder="输入允许的域名，用逗号分隔，或使用 * 允许所有域名"
                                            className="min-h-[80px]"
                                        />
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="notifications" className="h-full mt-0">
                        <Card className="h-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Bell className="h-5 w-5"/>
                                    通知设置
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* 邮件通知设置 */}
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Mail className="h-4 w-4"/>
                                            <Label>启用邮件通知</Label>
                                        </div>
                                        <Switch
                                            checked={systemConfig.enableEmailNotifications}
                                            onCheckedChange={handleSwitchChange('enableEmailNotifications')}
                                        />
                                    </div>

                                    {systemConfig.enableEmailNotifications && (
                                        <div className="grid grid-cols-2 gap-4 ml-6">
                                            <div className="space-y-2">
                                                <Label>SMTP 主机</Label>
                                                <Input
                                                    value={systemConfig.emailSmtpHost}
                                                    onChange={handleStringInputChange('emailSmtpHost')}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label>SMTP 端口</Label>
                                                <Input
                                                    type="number"
                                                    value={systemConfig.emailSmtpPort}
                                                    onChange={handleNumberInputChange('emailSmtpPort')}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label>用户名</Label>
                                                <Input
                                                    value={systemConfig.emailUsername}
                                                    onChange={handleStringInputChange('emailUsername')}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label>密码</Label>
                                                <Input
                                                    type="password"
                                                    value={systemConfig.emailPassword}
                                                    onChange={handleStringInputChange('emailPassword')}
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* 短信通知设置 */}
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Smartphone className="h-4 w-4"/>
                                            <Label>启用短信通知</Label>
                                        </div>
                                        <Switch
                                            checked={systemConfig.enableSmsNotifications}
                                            onCheckedChange={handleSwitchChange('enableSmsNotifications')}
                                        />
                                    </div>

                                    {systemConfig.enableSmsNotifications && (
                                        <div className="ml-6">
                                            <Badge variant="outline" className="text-xs">
                                                短信通知功能正在开发中
                                            </Badge>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </div>

                {/* 底部操作按钮 */}
                <div className="flex justify-end gap-2 mt-4 pt-4 border-t">
                    {saveStatus && (
                        <div className="flex items-center gap-2 mr-4">
                            {getSaveStatusIcon()}
                            <span className={`text-sm ${
                                saveStatus === 'success' ? 'text-status-success' : 'text-status-error'
                            }`}>
                                {saveStatus === 'success' ? '配置已保存' : '保存失败'}
                            </span>
                        </div>
                    )}

                    <Button variant="outline" onClick={handleReset}>
                        <RefreshCw className="h-4 w-4 mr-2"/>
                        重置
                    </Button>

                    <Button onClick={handleSave} disabled={isSaving}>
                        {isSaving ? (
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin"/>
                        ) : (
                            <Save className="h-4 w-4 mr-2"/>
                        )}
                        {isSaving ? '保存中...' : '保存配置'}
                    </Button>
                </div>
            </Tabs>
        </div>
    );
};
