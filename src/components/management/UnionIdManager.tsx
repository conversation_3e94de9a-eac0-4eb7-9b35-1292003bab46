import React, {useEffect, useState} from 'react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Textarea} from '@/components/ui/textarea';
import {Badge} from '@/components/ui/badge';
import {ScrollArea} from '@/components/ui/scroll-area';
import {Check, Copy, Download, Edit, MoreHorizontal, Plus, Save, Search, Trash2, Upload, Users, X} from 'lucide-react';
import {useChatStore} from '@/store/chatStore.ts';
import {UnionIdItem} from '@/service/types';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger,} from '@/components/ui/dialog';

// 类型定义
interface EditForm {
    id?: string;
    value?: string;
    name?: string;
    description?: string;
    createdAt?: Date;
    updatedAt?: Date;
    tags?: string[];
    isActive?: boolean;
}

type EditingState = string | null;
type CopiedState = string | null;

export const UnionIdManager: React.FC = () => {
    const {userConfig, updateUserConfig} = useChatStore();
    const [unionIds, setUnionIds] = useState<UnionIdItem[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
    const [editingId, setEditingId] = useState<EditingState>(null);
    const [editForm, setEditForm] = useState<EditForm>({});
    const [copiedId, setCopiedId] = useState<CopiedState>(null);
    const [showBatchImport, setShowBatchImport] = useState<boolean>(false);
    const [importText, setImportText] = useState<string>('');

    // 从用户配置中加载 unionIds
    useEffect(() => {
        if (userConfig.unionIds) {
            setUnionIds(userConfig.unionIds);
        }
    }, [userConfig]);

    // 保存 unionIds 到用户配置
    const saveUnionIds = async (newUnionIds: UnionIdItem[]): Promise<void> => {
        await updateUserConfig({unionIds: newUnionIds});
        setUnionIds(newUnionIds);
    };

    // 过滤 UnionId 列表
    const filteredUnionIds: UnionIdItem[] = unionIds.filter((item: UnionIdItem) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.value.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    // 添加新的 UnionId
    const handleAdd = (): void => {
        const newId: UnionIdItem = {
            id: Date.now().toString(),
            value: '',
            name: `用户 ${unionIds.length + 1}`,
            description: '',
            createdAt: new Date(),
            updatedAt: new Date(),
            tags: [],
            isActive: true
        };

        setEditingId(newId.id);
        setEditForm(newId);
        setUnionIds(prev => [newId, ...prev]);
    };

    // 开始编辑
    const handleEdit = (item: UnionIdItem): void => {
        setEditingId(item.id);
        setEditForm({...item});
    };

    // 保存编辑
    const handleSave = async (): Promise<void> => {
        if (!editForm.value?.trim() || !editForm.name?.trim()) {
            return;
        }

        const updatedIds: UnionIdItem[] = unionIds.map((item: UnionIdItem) =>
            item.id === editingId
                ? {
                    ...item,
                    value: editForm.value || '',
                    name: editForm.name || '',
                    description: editForm.description || '',
                    tags: editForm.tags || [],
                    isActive: editForm.isActive ?? true,
                    updatedAt: new Date()
                }
                : item
        );

        await saveUnionIds(updatedIds);
        setEditingId(null);
        setEditForm({});
    };

    // 取消编辑
    const handleCancel = (): void => {
        if (editForm.value === '' && editForm.name === `用户 ${unionIds.length}`) {
            // 如果是新添加的空项目，删除它
            setUnionIds(prev => prev.filter((item: UnionIdItem) => item.id !== editingId));
        }
        setEditingId(null);
        setEditForm({});
    };

    // 删除单个 UnionId
    const handleDelete = async (id: string): Promise<void> => {
        const updatedIds: UnionIdItem[] = unionIds.filter((item: UnionIdItem) => item.id !== id);
        await saveUnionIds(updatedIds);
        setSelectedIds(prev => {
            const newSet = new Set(prev);
            newSet.delete(id);
            return newSet;
        });
    };

    // 批量删除
    const handleBatchDelete = async (): Promise<void> => {
        const updatedIds: UnionIdItem[] = unionIds.filter((item: UnionIdItem) => !selectedIds.has(item.id));
        await saveUnionIds(updatedIds);
        setSelectedIds(new Set());
    };

    // 复制到剪贴板
    const handleCopy = async (value: string): Promise<void> => {
        try {
            await navigator.clipboard.writeText(value);
            setCopiedId(value);
            setTimeout(() => setCopiedId(null), 2000);
        } catch (error) {
            console.error('复制失败:', error);
        }
    };

    // 全选/取消全选
    const toggleSelectAll = (): void => {
        if (selectedIds.size === filteredUnionIds.length) {
            setSelectedIds(new Set());
        } else {
            setSelectedIds(new Set(filteredUnionIds.map((item: UnionIdItem) => item.id)));
        }
    };

    // 批量导入
    const handleBatchImport = async (): Promise<void> => {
        const lines: string[] = importText.split('\n').filter((line: string) => line.trim());
        const newIds: UnionIdItem[] = [];

        lines.forEach((line: string, index: number) => {
            const parts: string[] = line.split(',').map((part: string) => part.trim());
            if (parts.length >= 1 && parts[0]) {
                newIds.push({
                    id: `import_${Date.now()}_${index}`,
                    value: parts[0],
                    name: parts[1] || `导入用户 ${index + 1}`,
                    description: parts[2] || '',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    tags: [],
                    isActive: true
                });
            }
        });

        if (newIds.length > 0) {
            const updatedIds: UnionIdItem[] = [...newIds, ...unionIds];
            await saveUnionIds(updatedIds);
            setImportText('');
            setShowBatchImport(false);
        }
    };

    // 导出数据
    const handleExport = (): void => {
        const csvContent: string = unionIds.map((item: UnionIdItem) =>
            `${item.value},${item.name},${item.description || ''}`
        ).join('\n');

        const blob: Blob = new Blob([csvContent], {type: 'text/csv'});
        const url: string = URL.createObjectURL(blob);
        const a: HTMLAnchorElement = document.createElement('a');
        a.href = url;
        a.download = `unionids_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
    };

    // 事件处理函数
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
        setSearchQuery(e.target.value);
    };

    const handleImportTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
        setImportText(e.target.value);
    };

    const handleEditFormChange = (field: keyof EditForm) =>
        (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void => {
            setEditForm(prev => ({
                ...prev,
                [field]: e.target.value
            }));
        };

    const handleCheckboxChange = (id: string) =>
        (e: React.ChangeEvent<HTMLInputElement>): void => {
            if (e.target.checked) {
                setSelectedIds(prev => new Set([...prev, id]));
            } else {
                setSelectedIds(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(id);
                    return newSet;
                });
            }
        };

    return (
        <div className="h-full flex flex-col space-y-4">
            {/* 工具栏 */}
            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-2 flex-1">
                            <div className="relative flex-1 max-w-sm">
                                <Search
                                    className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"/>
                                <Input
                                    placeholder="搜索 UnionId..."
                                    value={searchQuery}
                                    onChange={handleSearchChange}
                                    className="pl-10"
                                />
                            </div>

                            {selectedIds.size > 0 && (
                                <div className="flex items-center gap-2">
                                    <Badge variant="secondary">
                                        已选择 {selectedIds.size} 项
                                    </Badge>
                                    <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={handleBatchDelete}
                                    >
                                        <Trash2 className="h-4 w-4 mr-1"/>
                                        批量删除
                                    </Button>
                                </div>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            <Button onClick={handleAdd} size="sm">
                                <Plus className="h-4 w-4 mr-1"/>
                                添加
                            </Button>

                            <Dialog open={showBatchImport} onOpenChange={setShowBatchImport}>
                                <DialogTrigger asChild>
                                    <Button variant="outline" size="sm">
                                        <Upload className="h-4 w-4 mr-1"/>
                                        批量导入
                                    </Button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>批量导入 UnionId</DialogTitle>
                                    </DialogHeader>
                                    <div className="space-y-4">
                                        <div>
                                            <Label>导入格式</Label>
                                            <p className="text-sm text-muted-foreground">
                                                每行一个，格式：UnionId,名称,描述（名称和描述可选）
                                            </p>
                                        </div>
                                        <Textarea
                                            placeholder="union123,用户1,测试用户&#10;union456,用户2,正式用户"
                                            value={importText}
                                            onChange={handleImportTextChange}
                                            className="min-h-[200px]"
                                        />
                                        <div className="flex justify-end gap-2">
                                            <Button variant="outline" onClick={() => setShowBatchImport(false)}>
                                                取消
                                            </Button>
                                            <Button onClick={handleBatchImport}>
                                                导入
                                            </Button>
                                        </div>
                                    </div>
                                </DialogContent>
                            </Dialog>

                            <Button variant="outline" size="sm" onClick={handleExport}>
                                <Download className="h-4 w-4 mr-1"/>
                                导出
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* 统计信息 */}
            <div className="grid grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold">{unionIds.length}</div>
                            <div className="text-sm text-muted-foreground">总数量</div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-center">
                            <div
                                className="text-2xl font-bold">{unionIds.filter((item: UnionIdItem) => item.isActive).length}</div>
                            <div className="text-sm text-muted-foreground">活跃用户</div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold">{filteredUnionIds.length}</div>
                            <div className="text-sm text-muted-foreground">搜索结果</div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold">{selectedIds.size}</div>
                            <div className="text-sm text-muted-foreground">已选择</div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* UnionId 列表 */}
            <Card className="flex-1 overflow-hidden">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>UnionId 列表</span>
                        {filteredUnionIds.length > 0 && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={toggleSelectAll}
                            >
                                {selectedIds.size === filteredUnionIds.length ? '取消全选' : '全选'}
                            </Button>
                        )}
                    </CardTitle>
                </CardHeader>
                <CardContent className="p-0 flex-1 overflow-hidden">
                    {filteredUnionIds.length === 0 ? (
                        <div className="text-center py-12 text-muted-foreground">
                            <Users className="h-12 w-12 mx-auto mb-4 opacity-50"/>
                            <p className="text-lg mb-2">
                                {searchQuery ? '未找到匹配的 UnionId' : '暂无 UnionId'}
                            </p>
                            <p className="text-sm">
                                {searchQuery ? '尝试调整搜索条件' : '点击添加按钮创建第一个 UnionId'}
                            </p>
                        </div>
                    ) : (
                        <ScrollArea className="h-full">
                            <div className="p-4 space-y-3">
                                {filteredUnionIds.map((item: UnionIdItem) => (
                                    <Card key={item.id} className="relative">
                                        <CardContent className="p-4">
                                            {editingId === item.id ? (
                                                // 编辑模式
                                                <div className="space-y-4">
                                                    <div className="grid grid-cols-2 gap-4">
                                                        <div>
                                                            <Label>名称</Label>
                                                            <Input
                                                                value={editForm.name || ''}
                                                                onChange={handleEditFormChange('name')}
                                                                placeholder="输入名称"
                                                            />
                                                        </div>
                                                        <div>
                                                            <Label>UnionId</Label>
                                                            <Input
                                                                value={editForm.value || ''}
                                                                onChange={handleEditFormChange('value')}
                                                                placeholder="输入 UnionId"
                                                                className="font-mono"
                                                            />
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <Label>描述</Label>
                                                        <Textarea
                                                            value={editForm.description || ''}
                                                            onChange={handleEditFormChange('description')}
                                                            placeholder="输入描述信息"
                                                            className="min-h-[80px]"
                                                        />
                                                    </div>
                                                    <div className="flex justify-end gap-2">
                                                        <Button variant="outline" size="sm" onClick={handleCancel}>
                                                            <X className="h-4 w-4 mr-1"/>
                                                            取消
                                                        </Button>
                                                        <Button size="sm" onClick={handleSave}>
                                                            <Save className="h-4 w-4 mr-1"/>
                                                            保存
                                                        </Button>
                                                    </div>
                                                </div>
                                            ) : (
                                                // 显示模式
                                                <div className="space-y-3">
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex items-center gap-3">
                                                            <input
                                                                type="checkbox"
                                                                checked={selectedIds.has(item.id)}
                                                                onChange={handleCheckboxChange(item.id)}
                                                                className="rounded"
                                                            />
                                                            <div>
                                                                <div className="flex items-center gap-2">
                                                                    <h3 className="font-medium">{item.name}</h3>
                                                                    {item.isActive && (
                                                                        <Badge variant="outline" className="text-xs">
                                                                            活跃
                                                                        </Badge>
                                                                    )}
                                                                </div>
                                                                <div
                                                                    className="text-sm text-muted-foreground font-mono">
                                                                    {item.value}
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div className="flex items-center gap-1">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => handleCopy(item.value)}
                                                                className="h-8 w-8 p-0"
                                                            >
                                                                {copiedId === item.value ? (
                                                                    <Check className="h-3 w-3 text-green-600"/>
                                                                ) : (
                                                                    <Copy className="h-3 w-3"/>
                                                                )}
                                                            </Button>

                                                            <DropdownMenu>
                                                                <DropdownMenuTrigger asChild>
                                                                    <Button variant="ghost" size="sm"
                                                                            className="h-8 w-8 p-0">
                                                                        <MoreHorizontal className="h-3 w-3"/>
                                                                    </Button>
                                                                </DropdownMenuTrigger>
                                                                <DropdownMenuContent align="end">
                                                                    <DropdownMenuItem onClick={() => handleEdit(item)}>
                                                                        <Edit className="h-4 w-4 mr-2"/>
                                                                        编辑
                                                                    </DropdownMenuItem>
                                                                    <DropdownMenuSeparator/>
                                                                    <DropdownMenuItem
                                                                        onClick={() => handleDelete(item.id)}
                                                                        className="text-destructive"
                                                                    >
                                                                        <Trash2 className="h-4 w-4 mr-2"/>
                                                                        删除
                                                                    </DropdownMenuItem>
                                                                </DropdownMenuContent>
                                                            </DropdownMenu>
                                                        </div>
                                                    </div>

                                                    {item.description && (
                                                        <p className="text-sm text-muted-foreground">
                                                            {item.description}
                                                        </p>
                                                    )}

                                                    <div
                                                        className="flex items-center justify-between text-xs text-muted-foreground">
                                                        <span>
                                                            创建于 {new Date(item.createdAt).toLocaleDateString()}
                                                        </span>
                                                        <span>
                                                            更新于 {new Date(item.updatedAt).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </div>
                                            )}
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </ScrollArea>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};
