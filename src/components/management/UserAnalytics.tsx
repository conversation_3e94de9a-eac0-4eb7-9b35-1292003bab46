import React, {useEffect, useState} from 'react';
import {<PERSON>, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {But<PERSON>} from '@/components/ui/button';
import {Badge} from '@/components/ui/badge';
import {ScrollArea} from '@/components/ui/scroll-area';
import {Activity, BarChart3, Clock, MessageSquare, TrendingDown, TrendingUp, Users} from 'lucide-react';
import {
    CartesianGrid,
    Cell,
    Line,
    LineChart,
    Pie,
    PieChart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts';

export const UserAnalytics = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [analyticsData, setAnalyticsData] = useState(null);

  // 模拟数据加载
  useEffect(() => {
    const loadAnalytics = () => {
      // 模拟 API 调用
      setTimeout(() => {
        setAnalyticsData(generateMockAnalytics());
      }, 1000);
    };
    
    loadAnalytics();
  }, [timeRange]);

  const generateMockAnalytics = () => {
    return {
      overview: {
        totalUsers: 1247,
        activeUsers: 892,
        totalSessions: 3456,
        avgSessionDuration: 12.5
      },
      trends: {
        userGrowth: 15.2,
        sessionGrowth: 8.7,
        engagementGrowth: -2.1
      },
      dailyStats: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
        users: Math.floor(Math.random() * 200) + 100,
        sessions: Math.floor(Math.random() * 500) + 200,
        duration: Math.floor(Math.random() * 20) + 5
      })),
      featureUsage: [
        { name: 'TraceId 分析', value: 45, color: '#8884d8' },
        { name: 'UnionId 查询', value: 30, color: '#82ca9d' },
        { name: 'FSD 链路', value: 15, color: '#ffc658' },
        { name: '其他功能', value: 10, color: '#ff7300' }
      ],
      topUsers: Array.from({ length: 10 }, (_, i) => ({
        id: `user_${i + 1}`,
        name: `用户 ${i + 1}`,
        sessions: Math.floor(Math.random() * 50) + 10,
        duration: Math.floor(Math.random() * 120) + 30,
        lastActive: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
      }))
    };
  };

  const timeRanges = [
    { value: '1d', label: '今天' },
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
    { value: '90d', label: '90天' }
  ];

  if (!analyticsData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-muted-foreground">加载分析数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* 时间范围选择 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">用户行为分析</h2>
            <div className="flex gap-2">
              {timeRanges.map((range) => (
                <Button
                  key={range.value}
                  variant={timeRange === range.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange(range.value)}
                >
                  {range.label}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 概览统计 */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">总用户数</p>
                <p className="text-2xl font-bold">{analyticsData.overview.totalUsers}</p>
              </div>
              <Users className="h-8 w-8 text-primary" />
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-sm text-green-600">+{analyticsData.trends.userGrowth}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">活跃用户</p>
                <p className="text-2xl font-bold">{analyticsData.overview.activeUsers}</p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-sm text-green-600">+{analyticsData.trends.sessionGrowth}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">总会话数</p>
                <p className="text-2xl font-bold">{analyticsData.overview.totalSessions}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-blue-600" />
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-sm text-green-600">+{analyticsData.trends.sessionGrowth}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">平均会话时长</p>
                <p className="text-2xl font-bold">{analyticsData.overview.avgSessionDuration}分钟</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
            <div className="flex items-center mt-2">
              <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
              <span className="text-sm text-red-600">{analyticsData.trends.engagementGrowth}%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-2 gap-4 flex-1">
        {/* 用户活跃度趋势 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">用户活跃度趋势</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={analyticsData.dailyStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="users" stroke="#8884d8" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 功能使用分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">功能使用分布</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={analyticsData.featureUsage}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {analyticsData.featureUsage.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 活跃用户排行 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">活跃用户排行</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-60">
            <div className="space-y-3">
              {analyticsData.topUsers.map((user, index) => (
                <div key={user.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-muted-foreground">
                        最后活跃: {user.lastActive.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{user.sessions} 次会话</p>
                    <p className="text-sm text-muted-foreground">{user.duration} 分钟</p>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};
