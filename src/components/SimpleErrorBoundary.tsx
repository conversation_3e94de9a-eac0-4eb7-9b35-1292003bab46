import React, {ErrorInfo, ReactNode} from 'react';

// 类型定义
interface Props {
    children: ReactNode;
}

interface State {
    hasError: boolean;
    error: Error | null;
    errorInfo: ErrorInfo | null;
    showDetails: boolean;
}

interface ErrorReport {
    message: string;
    stack?: string;
    componentStack?: string | null;
    timestamp: string;
    userAgent: string;
    url: string;
    userId: string;
}

class SimpleErrorBoundary extends React.Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            showDetails: false
        };
    }

    static getDerivedStateFromError(error: Error): Partial<State> {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        this.setState({ errorInfo });

        // 发送错误报告（生产环境）
        if (process.env.NODE_ENV === 'production') {
            this.reportError(error, errorInfo);
        }
    }

    reportError = (error: Error, errorInfo: ErrorInfo): void => {
        try {
            const errorReport: ErrorReport = {
                message: error.message,
                stack: error.stack,
                componentStack: errorInfo.componentStack,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                userId: localStorage.getItem('userId') ?? 'anonymous'
            };

            // 这里可以发送到错误监控服务
            console.log('Error Report:', errorReport);

            // 示例：发送到后端
            // fetch('/service/error-report', {
            //   method: 'POST',
            //   headers: { 'Content-Type': 'application/json' },
            //   body: JSON.stringify(errorReport)
            // }).catch(console.error);
        } catch (reportError) {
            console.error('Failed to report error:', reportError);
        }
    };

    toggleDetails = (): void => {
        this.setState(prev => ({ showDetails: !prev.showDetails }));
    };

    render(): ReactNode {
        if (this.state.hasError) {
            const { error, errorInfo, showDetails } = this.state;
            const isDevelopment: boolean = process.env.NODE_ENV === 'development';

            return (
                <div className="min-h-screen bg-background flex items-center justify-center p-4">
                    <div className="max-w-2xl mx-auto">
                        {/* 主要错误信息 */}
                        <div className="bg-card border border-destructive/20 rounded-lg p-6 shadow-lg">
                            <div className="text-center mb-6">
                                <div className="text-6xl mb-4">⚠️</div>
                                <h1 className="text-2xl font-bold text-foreground mb-2">
                                    应用遇到了错误
                                </h1>
                                <p className="text-muted-foreground">
                                    很抱歉，应用遇到了一个意外错误。我们已经记录了这个问题。
                                </p>
                            </div>

                            {/* 错误摘要 */}
                            <div className="bg-muted/30 rounded-md p-4 mb-6">
                                <h3 className="font-semibold text-sm mb-2">错误信息：</h3>
                                <p className="text-sm text-muted-foreground font-mono">
                                    {error?.message || '未知错误'}
                                </p>
                                <p className="text-xs text-muted-foreground mt-2">
                                    时间: {new Date().toLocaleString('zh-CN')}
                                </p>
                            </div>

                            {/* 操作按钮 */}
                            <div className="flex flex-col sm:flex-row gap-3 mb-4">
                                <button
                                    onClick={() => window.location.reload()}
                                    className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                                >
                                    🔄 刷新页面
                                </button>
                                <button
                                    onClick={() => { window.location.href = '/'; }}
                                    className="flex-1 px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors"
                                >
                                    🏠 返回首页
                                </button>
                                {(isDevelopment || showDetails) && (
                                    <button
                                        onClick={this.toggleDetails}
                                        className="px-4 py-2 bg-muted text-muted-foreground rounded-md hover:bg-muted/80 transition-colors"
                                    >
                                        {showDetails ? '隐藏详情' : '显示详情'}
                                    </button>
                                )}
                            </div>

                            {/* 详细错误信息（开发环境或用户选择显示） */}
                            {(isDevelopment || showDetails) && (
                                <details className="bg-muted/20 rounded-md p-4" open={isDevelopment}>
                                    <summary className="cursor-pointer font-semibold text-sm mb-2">
                                        技术详情 {isDevelopment ? '(开发模式)' : ''}
                                    </summary>
                                    <div className="space-y-3 text-xs">
                                        {error?.stack && (
                                            <div>
                                                <h4 className="font-semibold mb-1">错误堆栈:</h4>
                                                <pre className="bg-background p-2 rounded border overflow-auto text-xs">
                          {error.stack}
                        </pre>
                                            </div>
                                        )}
                                        {errorInfo?.componentStack && (
                                            <div>
                                                <h4 className="font-semibold mb-1">组件堆栈:</h4>
                                                <pre className="bg-background p-2 rounded border overflow-auto text-xs">
                          {errorInfo.componentStack}
                        </pre>
                                            </div>
                                        )}
                                    </div>
                                </details>
                            )}

                            {/* 帮助信息 */}
                            <div className="text-center text-xs text-muted-foreground mt-4 pt-4 border-t">
                                如果问题持续存在，请联系技术支持或尝试清除浏览器缓存
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

export default SimpleErrorBoundary;

