/* TraceTree 组件样式 */

/* 连线精确对齐样式 - 针对深层嵌套优化 */
.trace-tree-line {
    position: absolute;
    width: 1px;
    background-color: #10b981;
    /* 确保连线在像素边界上精确渲染，避免模糊 */
    transform: translateZ(0);
    will-change: transform;
    /* 强制使用整数像素，避免亚像素渲染 */
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    /* 确保在所有缩放级别下都能精确渲染 */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.trace-tree-connector {
    position: absolute;
    border-left: 1px solid #10b981;
    border-bottom: 1px solid #10b981;
    border-bottom-left-radius: 8px;
    /* 确保连接器在像素边界上精确渲染 */
    transform: translateZ(0);
    will-change: transform;
    /* 强制使用整数像素，避免亚像素渲染 */
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    /* 确保在所有缩放级别下都能精确渲染 */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* 深层嵌套时的特殊处理 */
.trace-tree-deep-level {
    /* 确保深层级元素的精确定位 */
    position: relative;
    box-sizing: border-box;
    /* 防止累积的舍入误差 */
    contain: layout;
}

/* 确保整个树结构的精确渲染 */
.trace-tree-container {
    /* 启用硬件加速，确保精确的像素对齐 */
    transform: translateZ(0);
    will-change: transform;
    /* 防止子元素的变换影响布局 */
    contain: layout style;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .css-em6946:hover {
        background-color: rgba(255, 255, 255, 0.08);
    }

    .bg-secondary-hover {
        background-color: rgba(255, 255, 255, 0.08);
    }

    .text-tertiary {
        color: #9ca3af;
    }
}

