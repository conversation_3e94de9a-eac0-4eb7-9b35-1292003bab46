/* TraceTree 组件样式 */
:root {
    --tree-py: 6px;
    --gray-200: #e5e7eb;
    --brand-green-400: #4ade80;
}

/* 垂直连线颜色 */
.text-brand-green-400 {
    color: #4ade80 !important;
}

.text-gray-200 {
    color: #e5e7eb !important;
}

/* 确保 bg-current 使用正确的颜色 */
.bg-current {
    background-color: currentColor;
}

/* 确保 border-current 使用正确的颜色 */
.border-current {
    border-color: currentColor;
}

/* MUI 样式类 */
.MuiListItemButton-root {
    display: flex;
    align-items: center;
    border: none;
    background: transparent;
    cursor: pointer;
    width: 100%;
    text-align: left;
    text-decoration: none;
    position: relative;
    min-height: 28px;
}

.MuiListItemButton-colorNeutral {
    color: inherit;
}

.MuiListItemButton-variantPlain {
    background: transparent;
}

.css-em6946 {
    /* 默认状态 */
}

.css-em6946:hover {
    background-color: rgba(0, 0, 0, 0.04);
    /* 移除所有动画效果 */
    transition: none;
}

.Joy-focusVisible.css-iwxvua {
    /* 选中状态 */
    background-color: rgba(59, 130, 246, 0.1);
    /* 移除边框，避免位置移动 */
    /* 移除所有动画效果 */
    transition: none;
}

/* 节点图标样式 */
._node_1ve55_7 {
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    min-height: 20px;
}

/* 文本颜色类 */
.text-tertiary {
    color: #6b7280;
}

.text-success {
    color: #10b981;
}

.text-error {
    color: #ef4444;
}

.text-warning {
    color: #f59e0b;
}

.text-primary {
    color: #3b82f6;
}

/* 边框颜色类 */
.border-success {
    border-color: #10b981;
}

.border-error {
    border-color: #ef4444;
}

.border-warning {
    border-color: #f59e0b;
}

.border-secondary {
    border-color: #e5e7eb;
}

.border-brand {
    border-color: #3b82f6;
}

/* 背景颜色类 */
.bg-error-primary {
    background-color: rgba(239, 68, 68, 0.1);
}

.bg-primary {
    background-color: rgba(59, 130, 246, 0.1);
}

.bg-secondary-hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* 连线精确对齐样式 */
.trace-tree-line {
    position: absolute;
    width: 1px;
    background-color: #10b981;
    /* 确保连线在像素边界上精确渲染 */
    transform: translateZ(0);
    will-change: transform;
}

.trace-tree-connector {
    position: absolute;
    border-left: 1px solid #10b981;
    border-bottom: 1px solid #10b981;
    border-bottom-left-radius: 8px;
    /* 确保连接器在像素边界上精确渲染 */
    transform: translateZ(0);
    will-change: transform;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .css-em6946:hover {
        background-color: rgba(255, 255, 255, 0.08);
    }

    .bg-secondary-hover {
        background-color: rgba(255, 255, 255, 0.08);
    }

    .text-tertiary {
        color: #9ca3af;
    }
}

