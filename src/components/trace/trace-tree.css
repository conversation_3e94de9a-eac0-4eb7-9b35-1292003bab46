/* TraceTree 组件样式 */

.trace-tree-line {
    position: absolute;
    width: 1px;
    background-color: #10b981;
    transform: translateZ(0);
    will-change: transform;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.trace-tree-connector {
    position: absolute;
    border-left: 1px solid #10b981;
    border-bottom: 1px solid #10b981;
    border-bottom-left-radius: 8px;
    transform: translateZ(0);
    will-change: transform;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.trace-tree-deep-level {
    position: relative;
    box-sizing: border-box;
    contain: layout;
}

.trace-tree-container {
    transform: translateZ(0);
    will-change: transform;
    contain: layout style;
}

