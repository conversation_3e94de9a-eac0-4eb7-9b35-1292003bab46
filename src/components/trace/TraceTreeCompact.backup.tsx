import React, {ReactElement, useState} from 'react';
import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import {Button} from '@/components/ui/button';
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from '@/components/ui/collapsible';
import './trace-tree.css';
import {
    AlertTriangle,
    Bot,
    CheckCircle,
    ChevronDown,
    ChevronRight,
    Clock,
    Database,
    Eye,
    EyeOff,
    Globe,
    Hash,
    Layers,
    MessageSquare,
    Search,
    Server,
    Tag,
    XCircle,
    Zap
} from 'lucide-react';

// 类型定义
type ServiceType = 'http' | 'thrift' | 'db' | 'redis' | 'mq' | 'agent' | 'search' | 'vector';
type StatusType = 'success' | 'error' | 'timeout' | 'pending';

// 定义更具体的类型而不是 any
interface RequestData {
    [key: string]: unknown;
}

interface ResponseData {
    [key: string]: unknown;
}

interface TraceNode {
    id: string;
    type: ServiceType;
    service?: string;
    method?: string;
    url?: string;
    description?: string;
    duration: number;
    status: StatusType;
    timestamp: Date;
    tags?: string[];
    children?: TraceNode[];
    request?: RequestData;
    response?: ResponseData;
    sql?: string;
}

interface TraceTreeCompactProps {
    traceData?: TraceNode;
    className?: string;
}

interface TraceNodeDetailCompactProps {
    node: TraceNode;
}

export const TraceTreeCompact: React.FC<TraceTreeCompactProps> = ({traceData, className = ''}) => {
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
    const [selectedNode, setSelectedNode] = useState<TraceNode | null>(null);

    const toggleNode = (nodeId: string): void => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(nodeId)) {
            newExpanded.delete(nodeId);
        } else {
            newExpanded.add(nodeId);
        }
        setExpandedNodes(newExpanded);
    };

    const getServiceIcon = (type: ServiceType): ReactElement => {
        switch (type) {
            case 'http':
                return <Globe className="h-3 w-3"/>;
            case 'thrift':
                return <Zap className="h-3 w-3"/>;
            case 'db':
                return <Database className="h-3 w-3"/>;
            case 'redis':
                return <Server className="h-3 w-3"/>;
            case 'mq':
                return <MessageSquare className="h-3 w-3"/>;
            case 'agent':
                return <Bot className="h-3 w-3"/>;
            case 'search':
                return <Search className="h-3 w-3"/>;
            case 'vector':
                return <Layers className="h-3 w-3"/>;
            default:
                return <Server className="h-3 w-3"/>;
        }
    };

    const getStatusIcon = (status: StatusType): ReactElement => {
        switch (status) {
            case 'success':
                return <CheckCircle className="h-3 w-3 text-green-600"/>;
            case 'error':
                return <XCircle className="h-3 w-3 text-red-600"/>;
            case 'timeout':
                return <AlertTriangle className="h-3 w-3 text-yellow-600"/>;
            default:
                return <Clock className="h-3 w-3 text-gray-400"/>;
        }
    };

    const getNodeTypeColor = (type: ServiceType): string => {
        switch (type) {
            case 'http':
                return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-300';
            case 'thrift':
                return 'bg-orange-50 border-orange-200 text-orange-800 dark:bg-orange-950/20 dark:border-orange-800 dark:text-orange-300';
            case 'db':
                return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-950/20 dark:border-green-800 dark:text-green-300';
            case 'redis':
                return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-950/20 dark:border-red-800 dark:text-red-300';
            case 'mq':
                return 'bg-purple-50 border-purple-200 text-purple-800 dark:bg-purple-950/20 dark:border-purple-800 dark:text-purple-300';
            case 'agent':
                return 'bg-indigo-50 border-indigo-200 text-indigo-800 dark:bg-indigo-950/20 dark:border-indigo-800 dark:text-indigo-300';
            case 'search':
                return 'bg-teal-50 border-teal-200 text-teal-800 dark:bg-teal-950/20 dark:border-teal-800 dark:text-teal-300';
            case 'vector':
                return 'bg-pink-50 border-pink-200 text-pink-800 dark:bg-pink-950/20 dark:border-pink-800 dark:text-pink-300';
            default:
                return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-950/20 dark:border-gray-800 dark:text-gray-300';
        }
    };

    const formatDuration = (duration: number): string => {
        if (duration < 1000) {
            return `${duration}ms`;
        }
        return `${(duration / 1000).toFixed(2)}s`;
    };

    const getNodeIcon = (type: ServiceType): ReactElement => {
        switch (type) {
            case 'agent':
                return <span className="text-sm">🦜</span>;
            case 'http':
                return <Globe className="size-4"/>;
            case 'thrift':
                return <Zap className="size-4"/>;
            case 'db':
                return <Database className="size-4"/>;
            case 'redis':
                return <Server className="size-4"/>;
            case 'mq':
                return <MessageSquare className="size-4"/>;
            case 'search':
                return <Search className="size-4"/>;
            case 'vector':
                return <Layers className="size-4"/>;
            default:
                return <Server className="size-4"/>;
        }
    };

    const getNodeIconStyle = (type: ServiceType): string => {
        switch (type) {
            case 'agent':
                return 'border border-brand';
            case 'http':
                return 'bg-[rgb(255,136,0)] text-white dark:bg-[rgb(255,136,0)]';
            case 'thrift':
                return 'bg-orange-500 text-white';
            case 'db':
                return 'bg-green-500 text-white';
            case 'redis':
                return 'bg-red-500 text-white';
            case 'mq':
                return 'bg-purple-500 text-white';
            case 'search':
                return 'bg-[rgb(17,177,129)] text-white dark:bg-[rgb(0,165,115)]';
            case 'vector':
                return 'bg-[rgb(230,128,189)] text-white dark:bg-[rgb(202,68,138)]';
            default:
                return 'bg-gray-500 text-white';
        }
    };

    const getStatusBadgeStyle = (status: StatusType): string => {
        switch (status) {
            case 'success':
                return 'border-success text-success';
            case 'error':
                return 'border-error text-error';
            case 'timeout':
                return 'border-warning text-warning';
            default:
                return 'border-gray-300 text-gray-500';
        }
    };

    // 渲染缩进连线 - 使用验证过的精确对齐逻辑
    const renderIndentLines = (level: number, isLast: boolean, ancestorLines: boolean[] = []): ReactElement[] => {
        if (level === 0) return [];

        const indentElements: ReactElement[] = [];
        const INDENT_WIDTH = 20; // 每层缩进宽度
        const LINE_OFFSET = 9.5; // 连线的精确位置

        for (let i = 0; i < level; i++) {
            const isCurrentLevel = i === level - 1;

            if (isCurrentLevel) {
                // 当前层级 - 显示弧形连接线
                indentElements.push(
                    <span
                        key={`current-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {/* 垂直线 - 如果不是最后一个兄弟节点 */}
                        {!isLast && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}

                        {/* 弧形连接线 */}
                        <span
                            className="trace-tree-connector"
                            style={{
                                position: 'absolute',
                                left: `${LINE_OFFSET}px`,
                                top: '0',
                                width: '10px',
                                height: 'calc(50% + 6px)'
                            }}
                        ></span>
                    </span>
                );
            } else {
                // 祖先层级 - 显示垂直连接线（如果需要）
                const shouldShowLine = ancestorLines[i];
                indentElements.push(
                    <span
                        key={`ancestor-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {shouldShowLine && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}
                    </span>
                );
            }
        }

        return indentElements;
    };

    const renderTraceNode = (node: TraceNode, level: number = 0, isLast: boolean = false, parentPath: string = '', parentHasMoreSiblings: boolean[] = []): ReactElement => {
        const hasChildren = Boolean(node.children && node.children.length > 0);
        const isExpanded = expandedNodes.has(node.id);
        const isSelected = selectedNode?.id === node.id;
        const nodePath = parentPath ? `${parentPath}.${node.id}` : node.id;

        return (
            <div key={node.id} data-index={level} data-item-index={level} style={{overflowAnchor: 'none'}}>
                <div
                    className={`MuiListItemButton-root MuiListItemButton-colorNeutral MuiListItemButton-variantPlain ${
                        isSelected ? 'Joy-focusVisible css-iwxvua' : 'css-em6946'
                    }`}
                    role="button"
                    tabIndex={0}
                    onClick={() => setSelectedNode(node)}
                >
                    {/* 连接线部分 */}
                    {level > 0 && renderIndentLines(level, isLast, parentHasMoreSiblings)}

                    {/* 图标和垂直线容器 - 参照示例HTML结构 */}
                    <div className="flex flex-col items-center relative" style={{paddingTop: 'var(--tree-py)'}}>
                        <div
                            className={`p-[3px] ${level === 0 ? 'border border-brand' : ''} _node_1ve55_7 ${getNodeIconStyle(node.type)}`}
                            data-state="closed">
                            {getNodeIcon(node.type)}
                        </div>
                        {/* 垂直连线 - 如果有子节点且已展开 */}
                        {hasChildren && isExpanded && (
                            <div
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: 'calc(50% - 0.5px)', // 精确居中，避免亚像素偏移
                                    top: '26px', // 图标高度 + padding
                                    height: 'calc(100% - 26px)',
                                    width: '1px',
                                    zIndex: 1
                                }}
                            ></div>
                        )}
                    </div>

                    {/* 节点内容 - 单行布局 */}
                    <div className="my-auto ml-2 grid w-full grid-cols-[1fr,auto] items-center py-1">
                        <div className="flex min-w-0 items-center gap-2">
                            <span className="text-sm leading-tight tracking-tighter font-medium truncate"
                                  data-state="closed">
                                {node.service || node.method || node.id}
                            </span>
                            <div className="flex items-center gap-1">
                                <span className="text-xs text-tertiary">{formatDuration(node.duration)}</span>
                            </div>
                            <div
                                className={`inline-flex w-auto items-center gap-1 rounded-md border bg-background font-medium uppercase tracking-wide ${getStatusBadgeStyle(node.status)} px-1 py-0.5 text-xs`}>
                                {getStatusIcon(node.status)}
                            </div>

                            {/* 标签信息 - 移到展开按钮前面 */}
                            <div
                                className="flex items-center gap-1 rounded-md text-xs bg-error-primary text-error pl-1 pr-1.5">
                                <Clock className="size-3"/>
                                {formatDuration(node.duration)}
                            </div>
                            <div
                                className="flex items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-background px-1 text-xs">
                                <Database className="h-3 w-3"/>
                                540
                            </div>
                            {node.tags && node.tags.map((tag: string, index: number) => (
                                <div key={index} className="flex items-center gap-0.5">
                                    <div
                                        className="flex items-center gap-1 whitespace-nowrap rounded-md border border-secondary bg-primary px-1 text-xs">
                                        <Tag className="h-3 w-3 text-tertiary"/>
                                        {tag}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* 展开/折叠按钮 */}
                        {hasChildren && (
                            <div className="flex items-center">
                                <button
                                    type="button"
                                    className="inline-flex items-center justify-center transition-all duration-200 rounded-sm text-primary border border-transparent hover:bg-secondary-hover border-none bg-transparent p-0.5"
                                    onClick={(e: React.MouseEvent) => {
                                        e.stopPropagation();
                                        toggleNode(node.id);
                                    }}
                                >
                                    {isExpanded ? (
                                        <ChevronDown className="h-4 w-4 flex-shrink-0"/>
                                    ) : (
                                        <ChevronRight className="h-4 w-4 flex-shrink-0"/>
                                    )}
                                </button>
                            </div>
                        )}
                    </div>
                </div>

                {/* 子节点 - 只有在展开时才渲染 */}
                {hasChildren && isExpanded && node.children && (
                    <>
                        {node.children.map((child: TraceNode, index: number) => {
                            // 计算祖先连线信息
                            const newAncestorLines = [...parentHasMoreSiblings];
                            // 当前节点是否还有后续兄弟节点（影响从当前节点延伸的垂直线）
                            newAncestorLines[level] = index < (node.children as TraceNode[]).length - 1;


                            return renderTraceNode(
                                child,
                                level + 1,
                                index === (node.children as TraceNode[]).length - 1,
                                nodePath,
                                newAncestorLines
                            );
                        })}
                    </>
                )}
            </div>
        );
    };

    // 生成示例数据（如果没有提供 traceData）- 包含深层嵌套用于测试连线对齐
    const sampleTraceData: TraceNode = traceData || {
        id: 'root',
        type: 'agent',
        service: "O'Keefe Inc-service",
        duration: 1560,
        status: 'success',
        timestamp: new Date(),
        tags: ['this-is-a-tag'],
        children: [
            {
                id: 'redis-cluster-1',
                type: 'redis',
                service: 'redis-cluster',
                duration: 1930,
                status: 'error',
                timestamp: new Date(),
                children: [
                    {
                        id: 'mysql-cluster-67',
                        type: 'db',
                        service: 'mysql-cluster-67',
                        duration: 1440,
                        status: 'timeout',
                        timestamp: new Date(),
                        children: [
                            {
                                id: 'redis-cluster-2',
                                type: 'redis',
                                service: 'redis-cluster',
                                duration: 1290,
                                status: 'success',
                                timestamp: new Date(),
                                children: [
                                    {
                                        id: 'schoppe-service',
                                        type: 'thrift',
                                        service: 'Schoppe and Sons-service',
                                        duration: 1440,
                                        status: 'success',
                                        timestamp: new Date(),
                                        children: [
                                            {
                                                id: 'mysql-cluster-30',
                                                type: 'db',
                                                service: 'mysql-cluster-30',
                                                duration: 1120,
                                                status: 'timeout',
                                                timestamp: new Date(),
                                                children: [
                                                    {
                                                        id: 'rabbitmq-cluster-1',
                                                        type: 'mq',
                                                        service: 'rabbitmq-cluster',
                                                        duration: 182,
                                                        status: 'error',
                                                        timestamp: new Date()
                                                    },
                                                    {
                                                        id: 'lynch-service',
                                                        type: 'http',
                                                        service: 'Lynch - Leuschke-service',
                                                        duration: 884,
                                                        status: 'timeout',
                                                        timestamp: new Date()
                                                    },
                                                    {
                                                        id: 'redis-cluster-3',
                                                        type: 'redis',
                                                        service: 'redis-cluster',
                                                        duration: 277,
                                                        status: 'success',
                                                        timestamp: new Date()
                                                    },
                                                    {
                                                        id: 'redis-cluster-4',
                                                        type: 'redis',
                                                        service: 'redis-cluster',
                                                        duration: 1040,
                                                        status: 'timeout',
                                                        timestamp: new Date()
                                                    }
                                                ]
                                            }
                                        ]
                                    },
                                    {
                                        id: 'wiza-service',
                                        type: 'http',
                                        service: 'Wiza Littel and Abernathy-service',
                                        duration: 838,
                                        status: 'success',
                                        timestamp: new Date(),
                                        children: [
                                            {
                                                id: 'gleason-service',
                                                type: 'http',
                                                service: 'Gleason - Kautzer-service',
                                                duration: 727,
                                                status: 'timeout',
                                                timestamp: new Date(),
                                                children: [
                                                    {
                                                        id: 'schinner-service',
                                                        type: 'thrift',
                                                        service: 'Schinner - Hills-service',
                                                        duration: 505,
                                                        status: 'error',
                                                        timestamp: new Date()
                                                    },
                                                    {
                                                        id: 'rippin-service',
                                                        type: 'http',
                                                        service: 'Rippin, Rogahn and Davis-service',
                                                        duration: 772,
                                                        status: 'success',
                                                        timestamp: new Date()
                                                    }
                                                ]
                                            },
                                            {
                                                id: 'redis-cluster-5',
                                                type: 'redis',
                                                service: 'redis-cluster',
                                                duration: 553,
                                                status: 'timeout',
                                                timestamp: new Date(),
                                                children: [
                                                    {
                                                        id: 'satterfield-service',
                                                        type: 'http',
                                                        service: 'Satterfield - Frami-service',
                                                        duration: 840,
                                                        status: 'error',
                                                        timestamp: new Date()
                                                    },
                                                    {
                                                        id: 'redis-cluster-6',
                                                        type: 'redis',
                                                        service: 'redis-cluster',
                                                        duration: 15,
                                                        status: 'success',
                                                        timestamp: new Date()
                                                    },
                                                    {
                                                        id: 'rabbitmq-cluster-2',
                                                        type: 'mq',
                                                        service: 'rabbitmq-cluster',
                                                        duration: 118,
                                                        status: 'timeout',
                                                        timestamp: new Date()
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                id: 'rabbitmq-cluster-3',
                type: 'mq',
                service: 'rabbitmq-cluster',
                duration: 1350,
                status: 'error',
                timestamp: new Date()
            }
        ]
    };

    return (
        <div className={`space-y-3 ${className}`}>
            {/* 链路概览 */}
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Hash className="h-4 w-4"/>
                            链路追踪详情
                        </div>
                        <div className="flex gap-1">
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs px-2"
                                onClick={() => setExpandedNodes(new Set([sampleTraceData.id]))}
                            >
                                <EyeOff className="h-3 w-3 mr-1"/>
                                折叠
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs px-2"
                                onClick={() => {
                                    const allIds = new Set<string>();
                                    const collectIds = (node: TraceNode): void => {
                                        allIds.add(node.id);
                                        if (node.children) {
                                            node.children.forEach(collectIds);
                                        }
                                    };
                                    collectIds(sampleTraceData);
                                    setExpandedNodes(allIds);
                                }}
                            >
                                <Eye className="h-3 w-3 mr-1"/>
                                展开
                            </Button>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent className="p-3">
                    {/* 总体统计 */}
                    <div className="grid grid-cols-3 gap-3 mb-3 text-center">
                        <div className="p-2 bg-muted/30 rounded">
                            <div className="text-sm font-semibold">{formatDuration(sampleTraceData.duration)}</div>
                            <div className="text-xs text-muted-foreground">总耗时</div>
                        </div>
                        <div className="p-2 bg-muted/30 rounded">
                            <div className="text-sm font-semibold">540</div>
                            <div className="text-xs text-muted-foreground">令牌数</div>
                        </div>
                        <div className="p-2 bg-muted/30 rounded">
                            <div className="text-sm font-semibold">
                                {sampleTraceData.tags ? sampleTraceData.tags.length : 0}
                            </div>
                            <div className="text-xs text-muted-foreground">标签</div>
                        </div>
                    </div>

                    {/* 链路树 - 简洁的列表样式 */}
                    <div className="relative trace-tree-container">
                        {renderTraceNode(sampleTraceData, 0, false, '', [])}
                    </div>
                </CardContent>
            </Card>

            {/* 选中节点详情 */}
            {selectedNode && (
                <TraceNodeDetailCompact node={selectedNode}/>
            )}
        </div>
    );
};

// 紧凑版节点详情组件
const TraceNodeDetailCompact: React.FC<TraceNodeDetailCompactProps> = ({node}) => {
    const [showDetails, setShowDetails] = useState<boolean>(false);

    const getServiceIcon = (type: ServiceType): ReactElement => {
        switch (type) {
            case 'http':
                return <Globe className="h-3 w-3"/>;
            case 'thrift':
                return <Zap className="h-3 w-3"/>;
            case 'db':
                return <Database className="h-3 w-3"/>;
            case 'redis':
                return <Server className="h-3 w-3"/>;
            case 'mq':
                return <MessageSquare className="h-3 w-3"/>;
            case 'agent':
                return <Bot className="h-3 w-3"/>;
            case 'search':
                return <Search className="h-3 w-3"/>;
            case 'vector':
                return <Layers className="h-3 w-3"/>;
            default:
                return <Server className="h-3 w-3"/>;
        }
    };

    const getNodeTypeColor = (type: ServiceType): string => {
        switch (type) {
            case 'http':
                return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-300';
            case 'thrift':
                return 'bg-orange-50 border-orange-200 text-orange-800 dark:bg-orange-950/20 dark:border-orange-800 dark:text-orange-300';
            case 'db':
                return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-950/20 dark:border-green-800 dark:text-green-300';
            case 'redis':
                return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-950/20 dark:border-red-800 dark:text-red-300';
            case 'mq':
                return 'bg-purple-50 border-purple-200 text-purple-800 dark:bg-purple-950/20 dark:border-purple-800 dark:text-purple-300';
            case 'agent':
                return 'bg-indigo-50 border-indigo-200 text-indigo-800 dark:bg-indigo-950/20 dark:border-indigo-800 dark:text-indigo-300';
            case 'search':
                return 'bg-teal-50 border-teal-200 text-teal-800 dark:bg-teal-950/20 dark:border-teal-800 dark:text-teal-300';
            case 'vector':
                return 'bg-pink-50 border-pink-200 text-pink-800 dark:bg-pink-950/20 dark:border-pink-800 dark:text-pink-300';
            default:
                return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-950/20 dark:border-gray-800 dark:text-gray-300';
        }
    };

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                    {/* 服务图标 */}
                    <div className={`p-1 rounded border ${getNodeTypeColor(node.type)}`}>
                        {getServiceIcon(node.type)}
                    </div>
                    <span>{node.service || node.id} 详情</span>
                    <Badge variant="outline" className="text-xs h-4">
                        {node.type.toUpperCase()}
                    </Badge>
                </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
                {/* 基本信息 */}
                <div className="grid grid-cols-3 gap-3 mb-3 p-2 bg-muted/30 rounded text-center">
                    <div>
                        <div className="text-sm font-semibold">{node.duration}ms</div>
                        <div className="text-xs text-muted-foreground">耗时</div>
                    </div>
                    <div>
                        <div className="text-sm font-semibold">{node.status}</div>
                        <div className="text-xs text-muted-foreground">状态</div>
                    </div>
                    <div>
                        <div className="text-sm font-semibold">
                            {new Date(node.timestamp).toLocaleTimeString()}
                        </div>
                        <div className="text-xs text-muted-foreground">时间</div>
                    </div>
                </div>

                {/* 展开详细信息按钮 */}
                <Collapsible open={showDetails} onOpenChange={setShowDetails}>
                    <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full justify-between p-0 h-auto text-xs">
                            <span>详细信息</span>
                            {showDetails ? <ChevronDown className="h-3 w-3"/> : <ChevronRight className="h-3 w-3"/>}
                        </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                        <div className="mt-2 space-y-2">
                            {/* 请求信息 */}
                            {node.request && (
                                <div>
                                    <div className="text-xs font-medium mb-1">请求信息:</div>
                                    <div className="bg-muted/50 rounded p-2 text-xs">
                    <pre className="whitespace-pre-wrap overflow-x-auto">
                      {JSON.stringify(node.request, null, 2)}
                    </pre>
                                    </div>
                                </div>
                            )}

                            {/* 响应信息 */}
                            {node.response && (
                                <div>
                                    <div className="text-xs font-medium mb-1">响应信息:</div>
                                    <div className="bg-muted/50 rounded p-2 text-xs">
                    <pre className="whitespace-pre-wrap overflow-x-auto">
                      {JSON.stringify(node.response, null, 2)}
                    </pre>
                                    </div>
                                </div>
                            )}

                            {/* SQL 查询 */}
                            {node.sql && (
                                <div>
                                    <div className="text-xs font-medium mb-1">SQL 查询:</div>
                                    <div className="bg-muted/50 rounded p-2 text-xs">
                    <pre className="whitespace-pre-wrap overflow-x-auto">
                      {node.sql}
                    </pre>
                                    </div>
                                </div>
                            )}
                        </div>
                    </CollapsibleContent>
                </Collapsible>
            </CardContent>
        </Card>
    );
};
