import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff } from 'lucide-react';

// 简化的测试组件，专门用于验证连线对齐
export const TraceTreeAlignmentTest: React.FC = () => {
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root', 'level1', 'level2', 'level3', 'level4']));

    // 创建深层嵌套的测试数据
    const testData = {
        id: 'root',
        name: 'Root Service',
        children: [
            {
                id: 'level1-1',
                name: 'Level 1 Service A',
                children: [
                    {
                        id: 'level2-1',
                        name: 'Level 2 Service A1',
                        children: [
                            {
                                id: 'level3-1',
                                name: 'Level 3 Service A1a',
                                children: [
                                    {
                                        id: 'level4-1',
                                        name: 'Level 4 Service A1a1',
                                        children: [
                                            {
                                                id: 'level5-1',
                                                name: 'Level 5 Service A1a1i',
                                                children: []
                                            },
                                            {
                                                id: 'level5-2',
                                                name: 'Level 5 Service A1a1ii',
                                                children: []
                                            }
                                        ]
                                    },
                                    {
                                        id: 'level4-2',
                                        name: 'Level 4 Service A1a2',
                                        children: []
                                    }
                                ]
                            },
                            {
                                id: 'level3-2',
                                name: 'Level 3 Service A1b',
                                children: []
                            }
                        ]
                    },
                    {
                        id: 'level2-2',
                        name: 'Level 2 Service A2',
                        children: []
                    }
                ]
            },
            {
                id: 'level1-2',
                name: 'Level 1 Service B',
                children: [
                    {
                        id: 'level2-3',
                        name: 'Level 2 Service B1',
                        children: []
                    }
                ]
            }
        ]
    };

    const toggleExpanded = (nodeId: string) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(nodeId)) {
            newExpanded.delete(nodeId);
        } else {
            newExpanded.add(nodeId);
        }
        setExpandedNodes(newExpanded);
    };

    const renderTestNode = (node: any, level: number, isLast: boolean, ancestorLines: boolean[]): React.ReactElement => {
        const isExpanded = expandedNodes.has(node.id);
        const hasChildren = node.children && node.children.length > 0;

        // 计算当前层级的祖先连线状态
        const currentAncestorLines = [...ancestorLines];
        if (level > 0) {
            currentAncestorLines[level - 1] = !isLast;
        }

        return (
            <div key={node.id} className="relative">
                <div className="flex items-center min-h-[28px]">
                    {/* 渲染缩进连线 */}
                    {level > 0 && renderIndentLines(level, isLast, ancestorLines)}

                    {/* 节点内容 */}
                    <div className="flex items-center gap-2 flex-1">
                        <div className="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-sm"></div>
                        </div>
                        <span className="text-sm">{node.name}</span>
                        {hasChildren && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0"
                                onClick={() => toggleExpanded(node.id)}
                            >
                                {isExpanded ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                            </Button>
                        )}
                    </div>
                </div>

                {/* 渲染子节点 */}
                {hasChildren && isExpanded && (
                    <div className="relative">
                        {node.children.map((child: any, index: number) => 
                            renderTestNode(child, level + 1, index === node.children.length - 1, currentAncestorLines)
                        )}
                    </div>
                )}
            </div>
        );
    };

    const renderIndentLines = (level: number, isLast: boolean, ancestorLines: boolean[]): React.ReactElement[] => {
        const indentElements: React.ReactElement[] = [];
        const INDENT_WIDTH = 20;
        const LINE_OFFSET = 9.5;

        for (let i = 0; i < level; i++) {
            const isCurrentLevel = i === level - 1;

            if (isCurrentLevel) {
                // 当前层级 - 显示弧形连接线
                indentElements.push(
                    <span
                        key={`current-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {/* 垂直线 - 如果不是最后一个兄弟节点 */}
                        {!isLast && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}

                        {/* 弧形连接线 */}
                        <span
                            className="trace-tree-connector"
                            style={{
                                position: 'absolute',
                                left: `${LINE_OFFSET}px`,
                                top: '0',
                                width: '10px',
                                height: 'calc(50% + 6px)'
                            }}
                        ></span>
                    </span>
                );
            } else {
                // 祖先层级 - 显示垂直连接线（如果需要）
                const shouldShowLine = ancestorLines[i];
                indentElements.push(
                    <span
                        key={`ancestor-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {shouldShowLine && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}
                    </span>
                );
            }
        }

        return indentElements;
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="text-lg flex items-center justify-between">
                    <span>连线对齐测试</span>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setExpandedNodes(new Set())}
                        >
                            全部折叠
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setExpandedNodes(new Set(['root', 'level1-1', 'level1-2', 'level2-1', 'level2-2', 'level2-3', 'level3-1', 'level3-2', 'level4-1', 'level4-2']))}
                        >
                            全部展开
                        </Button>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="trace-tree-container">
                    {renderTestNode(testData, 0, true, [])}
                </div>
            </CardContent>
        </Card>
    );
};
