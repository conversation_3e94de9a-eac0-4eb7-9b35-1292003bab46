import React, { useState, ReactElement } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
    ChevronDown,
    ChevronRight,
    Eye,
    EyeOff,
    Hash,
    Globe,
    Database,
    Server,
    MessageSquare,
    Bot,
    Search,
    Layers,
    Zap,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle
} from 'lucide-react';
import './trace-tree.css';

// 类型定义
export type ServiceType = 'http' | 'thrift' | 'db' | 'redis' | 'mq' | 'agent' | 'search' | 'vector';
export type NodeStatus = 'success' | 'error' | 'timeout' | 'pending';

export interface TraceNode {
    id: string;
    type: ServiceType;
    service?: string;
    method?: string;
    duration: number;
    status: NodeStatus;
    timestamp: Date;
    tags?: string[];
    children?: TraceNode[];
    request?: any;
    response?: any;
    sql?: string;
}

interface TraceTreeCompactProps {
    traceData?: TraceNode;
}

export const TraceTreeCompact: React.FC<TraceTreeCompactProps> = ({ traceData }) => {
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
    const [selectedNode, setSelectedNode] = useState<TraceNode | null>(null);

    // 切换节点展开状态
    const toggleExpanded = (nodeId: string) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(nodeId)) {
            newExpanded.delete(nodeId);
        } else {
            newExpanded.add(nodeId);
        }
        setExpandedNodes(newExpanded);
    };

    // 获取服务图标
    const getServiceIcon = (type: ServiceType): ReactElement => {
        switch (type) {
            case 'http': return <Globe className="h-3 w-3"/>;
            case 'thrift': return <Zap className="h-3 w-3"/>;
            case 'db': return <Database className="h-3 w-3"/>;
            case 'redis': return <Server className="h-3 w-3"/>;
            case 'mq': return <MessageSquare className="h-3 w-3"/>;
            case 'agent': return <Bot className="h-3 w-3"/>;
            case 'search': return <Search className="h-3 w-3"/>;
            case 'vector': return <Layers className="h-3 w-3"/>;
            default: return <Server className="h-3 w-3"/>;
        }
    };

    // 获取状态图标
    const getStatusIcon = (status: NodeStatus): ReactElement => {
        switch (status) {
            case 'success': return <CheckCircle className="h-3 w-3 text-green-500"/>;
            case 'error': return <XCircle className="h-3 w-3 text-red-500"/>;
            case 'timeout': return <AlertCircle className="h-3 w-3 text-yellow-500"/>;
            case 'pending': return <Clock className="h-3 w-3 text-blue-500"/>;
            default: return <CheckCircle className="h-3 w-3 text-gray-500"/>;
        }
    };

    // 获取节点类型颜色
    const getNodeTypeColor = (type: ServiceType): string => {
        switch (type) {
            case 'http': return 'bg-blue-50 border-blue-200 text-blue-800';
            case 'thrift': return 'bg-orange-50 border-orange-200 text-orange-800';
            case 'db': return 'bg-green-50 border-green-200 text-green-800';
            case 'redis': return 'bg-red-50 border-red-200 text-red-800';
            case 'mq': return 'bg-purple-50 border-purple-200 text-purple-800';
            case 'agent': return 'bg-indigo-50 border-indigo-200 text-indigo-800';
            case 'search': return 'bg-teal-50 border-teal-200 text-teal-800';
            case 'vector': return 'bg-pink-50 border-pink-200 text-pink-800';
            default: return 'bg-gray-50 border-gray-200 text-gray-800';
        }
    };

    // 格式化持续时间
    const formatDuration = (duration: number): string => {
        if (duration < 1000) return `${duration}ms`;
        return `${(duration / 1000).toFixed(2)}s`;
    };

    const renderIndentLines = (level: number, isLast: boolean, ancestorLines: boolean[] = []): ReactElement[] => {
        if (level === 0) return [];

        const indentElements: ReactElement[] = [];
        const INDENT_WIDTH = 20;
        const LINE_OFFSET = 9.5;

        for (let i = 0; i < level; i++) {
            const isCurrentLevel = i === level - 1;

            if (isCurrentLevel) {
                indentElements.push(
                    <span
                        key={`current-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {!isLast && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}

                        <span
                            className="trace-tree-connector"
                            style={{
                                position: 'absolute',
                                left: `${LINE_OFFSET}px`,
                                top: '0',
                                width: '10px',
                                height: 'calc(50% + 6px)'
                            }}
                        ></span>
                    </span>
                );
            } else {
                const shouldShowLine = ancestorLines[i];
                indentElements.push(
                    <span
                        key={`ancestor-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {shouldShowLine && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}
                    </span>
                );
            }
        }

        return indentElements;
    };

    const renderNode = (node: TraceNode, level: number, isLast: boolean, ancestorLines: boolean[]): ReactElement => {
        const isExpanded = expandedNodes.has(node.id);
        const hasChildren = node.children && node.children.length > 0;

        const currentAncestorLines = [...ancestorLines];
        if (level > 0) {
            currentAncestorLines[level - 1] = !isLast;
        }

        return (
            <div key={node.id} className="relative">
                <div
                    className={`flex items-center min-h-[28px] hover:bg-muted/50 cursor-pointer rounded-sm px-1 ${
                        selectedNode?.id === node.id ? 'bg-muted' : ''
                    }`}
                    onClick={() => setSelectedNode(node)}
                >
                    {level > 0 && renderIndentLines(level, isLast, ancestorLines)}

                    <div className="flex items-center gap-2 flex-1 min-w-0">
                        <div className={`p-1 rounded border flex-shrink-0 ${getNodeTypeColor(node.type)}`}>
                            {getServiceIcon(node.type)}
                        </div>

                        <span className="text-sm font-medium truncate flex-1">
                            {node.service || node.method || node.id}
                        </span>

                        <span className="text-xs text-muted-foreground flex-shrink-0">
                            {formatDuration(node.duration)}
                        </span>

                        <div className="flex-shrink-0">
                            {getStatusIcon(node.status)}
                        </div>

                        {hasChildren && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0 flex-shrink-0"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleExpanded(node.id);
                                }}
                            >
                                {isExpanded ? <ChevronDown className="h-3 w-3"/> : <ChevronRight className="h-3 w-3"/>}
                            </Button>
                        )}
                    </div>
                </div>

                {hasChildren && isExpanded && (
                    <div className="relative">
                        {node.children!.map((child, index) =>
                            renderNode(
                                child,
                                level + 1,
                                index === node.children!.length - 1,
                                currentAncestorLines
                            )
                        )}
                    </div>
                )}
            </div>
        );
    };

    const sampleData: TraceNode = traceData || {
        id: 'root',
        type: 'agent',
        service: "O'Keefe Inc-service",
        method: 'processRequest',
        duration: 1560,
        status: 'success',
        timestamp: new Date(),
        tags: ['production', 'high-priority', 'user-request'],
        request: {
            method: 'POST',
            url: '/api/v1/process',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ***'
            },
            body: {
                userId: '12345',
                action: 'process_data'
            }
        },
        response: {
            status: 200,
            data: {
                result: 'success',
                processedItems: 42
            }
        },
        children: [
            {
                id: 'redis-1',
                type: 'redis',
                service: 'redis-cluster',
                method: 'GET',
                duration: 1930,
                status: 'error',
                timestamp: new Date(),
                tags: ['cache', 'read-operation'],
                request: {
                    command: 'GET',
                    key: 'user:12345:profile'
                },
                response: {
                    error: 'Connection timeout',
                    code: 'TIMEOUT'
                },
                children: [
                    {
                        id: 'mysql-1',
                        type: 'db',
                        service: 'mysql-cluster-67',
                        method: 'SELECT',
                        duration: 1440,
                        status: 'timeout',
                        timestamp: new Date(),
                        tags: ['database', 'user-data'],
                        sql: 'SELECT id, name, email, profile_data FROM users WHERE id = ? AND status = ?',
                        request: {
                            query: 'SELECT users',
                            params: ['12345', 'active']
                        },
                        children: [
                            {
                                id: 'redis-2',
                                type: 'redis',
                                service: 'redis-cluster',
                                method: 'SET',
                                duration: 1290,
                                status: 'success',
                                timestamp: new Date(),
                                tags: ['cache', 'write-operation'],
                                request: {
                                    command: 'SET',
                                    key: 'user:12345:profile',
                                    value: '{"id":12345,"name":"John Doe"}'
                                },
                                response: {
                                    result: 'OK'
                                },
                                children: [
                                    {
                                        id: 'thrift-1',
                                        type: 'thrift',
                                        service: 'Schoppe and Sons-service',
                                        method: 'getUserPreferences',
                                        duration: 1440,
                                        status: 'success',
                                        timestamp: new Date(),
                                        tags: ['rpc', 'user-preferences'],
                                        request: {
                                            method: 'getUserPreferences',
                                            args: {
                                                userId: '12345',
                                                includeDefaults: true
                                            }
                                        },
                                        response: {
                                            preferences: {
                                                theme: 'dark',
                                                language: 'zh-CN',
                                                notifications: true
                                            }
                                        },
                                        children: []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    };

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Hash className="h-4 w-4"/>
                            链路追踪详情
                        </div>
                        <div className="flex gap-1">
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs px-2"
                                onClick={() => setExpandedNodes(new Set())}
                            >
                                <EyeOff className="h-3 w-3 mr-1"/>
                                折叠
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs px-2"
                                onClick={() => {
                                    const allIds = new Set<string>();
                                    const collectIds = (node: TraceNode) => {
                                        allIds.add(node.id);
                                        if (node.children) {
                                            node.children.forEach(collectIds);
                                        }
                                    };
                                    collectIds(sampleData);
                                    setExpandedNodes(allIds);
                                }}
                            >
                                <Eye className="h-3 w-3 mr-1"/>
                                展开
                            </Button>
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent className="p-3">
                    <div className="trace-tree-container">
                        {renderNode(sampleData, 0, true, [])}
                    </div>
                </CardContent>
            </Card>

            {/* 选中节点详情 */}
            {selectedNode && <TraceNodeDetail node={selectedNode} />}
        </div>
    );
};

// 节点详情组件
const TraceNodeDetail: React.FC<{ node: TraceNode }> = ({ node }) => {
    const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic']));

    const toggleSection = (section: string) => {
        const newExpanded = new Set(expandedSections);
        if (newExpanded.has(section)) {
            newExpanded.delete(section);
        } else {
            newExpanded.add(section);
        }
        setExpandedSections(newExpanded);
    };

    const getServiceIcon = (type: ServiceType): ReactElement => {
        switch (type) {
            case 'http': return <Globe className="h-4 w-4"/>;
            case 'thrift': return <Zap className="h-4 w-4"/>;
            case 'db': return <Database className="h-4 w-4"/>;
            case 'redis': return <Server className="h-4 w-4"/>;
            case 'mq': return <MessageSquare className="h-4 w-4"/>;
            case 'agent': return <Bot className="h-4 w-4"/>;
            case 'search': return <Search className="h-4 w-4"/>;
            case 'vector': return <Layers className="h-4 w-4"/>;
            default: return <Server className="h-4 w-4"/>;
        }
    };

    const getStatusIcon = (status: NodeStatus): ReactElement => {
        switch (status) {
            case 'success': return <CheckCircle className="h-4 w-4 text-green-500"/>;
            case 'error': return <XCircle className="h-4 w-4 text-red-500"/>;
            case 'timeout': return <AlertCircle className="h-4 w-4 text-yellow-500"/>;
            case 'pending': return <Clock className="h-4 w-4 text-blue-500"/>;
            default: return <CheckCircle className="h-4 w-4 text-gray-500"/>;
        }
    };

    const getNodeTypeColor = (type: ServiceType): string => {
        switch (type) {
            case 'http': return 'bg-blue-50 border-blue-200 text-blue-800';
            case 'thrift': return 'bg-orange-50 border-orange-200 text-orange-800';
            case 'db': return 'bg-green-50 border-green-200 text-green-800';
            case 'redis': return 'bg-red-50 border-red-200 text-red-800';
            case 'mq': return 'bg-purple-50 border-purple-200 text-purple-800';
            case 'agent': return 'bg-indigo-50 border-indigo-200 text-indigo-800';
            case 'search': return 'bg-teal-50 border-teal-200 text-teal-800';
            case 'vector': return 'bg-pink-50 border-pink-200 text-pink-800';
            default: return 'bg-gray-50 border-gray-200 text-gray-800';
        }
    };

    const formatDuration = (duration: number): string => {
        if (duration < 1000) return `${duration}ms`;
        return `${(duration / 1000).toFixed(2)}s`;
    };

    const formatTimestamp = (timestamp: Date): string => {
        return timestamp.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3
        });
    };

    return (
        <Card>
            <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                    <div className={`p-2 rounded border ${getNodeTypeColor(node.type)}`}>
                        {getServiceIcon(node.type)}
                    </div>
                    <div className="flex-1">
                        <div className="flex items-center gap-2">
                            <span>{node.service || node.method || node.id}</span>
                            {getStatusIcon(node.status)}
                        </div>
                        <div className="text-sm text-muted-foreground font-normal">
                            {node.type.toUpperCase()} • {formatDuration(node.duration)}
                        </div>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {/* 基本信息 */}
                <Collapsible
                    open={expandedSections.has('basic')}
                    onOpenChange={() => toggleSection('basic')}
                >
                    <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                            <span className="font-medium">基本信息</span>
                            <ChevronDown className={`h-4 w-4 transition-transform ${
                                expandedSections.has('basic') ? 'rotate-180' : ''
                            }`} />
                        </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span className="text-muted-foreground">节点ID:</span>
                                <div className="font-mono">{node.id}</div>
                            </div>
                            <div>
                                <span className="text-muted-foreground">服务类型:</span>
                                <div className="flex items-center gap-1">
                                    {getServiceIcon(node.type)}
                                    <span className="capitalize">{node.type}</span>
                                </div>
                            </div>
                            <div>
                                <span className="text-muted-foreground">执行时间:</span>
                                <div>{formatDuration(node.duration)}</div>
                            </div>
                            <div>
                                <span className="text-muted-foreground">状态:</span>
                                <div className="flex items-center gap-1">
                                    {getStatusIcon(node.status)}
                                    <span className="capitalize">{node.status}</span>
                                </div>
                            </div>
                            <div className="col-span-2">
                                <span className="text-muted-foreground">时间戳:</span>
                                <div className="font-mono">{formatTimestamp(node.timestamp)}</div>
                            </div>
                        </div>
                    </CollapsibleContent>
                </Collapsible>

                {/* 标签信息 */}
                {node.tags && node.tags.length > 0 && (
                    <Collapsible
                        open={expandedSections.has('tags')}
                        onOpenChange={() => toggleSection('tags')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">标签</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('tags') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <div className="flex flex-wrap gap-2">
                                {node.tags.map((tag, index) => (
                                    <Badge key={index} variant="secondary" className="text-xs">
                                        {tag}
                                    </Badge>
                                ))}
                            </div>
                        </CollapsibleContent>
                    </Collapsible>
                )}

                {/* 请求信息 */}
                {node.request && (
                    <Collapsible
                        open={expandedSections.has('request')}
                        onOpenChange={() => toggleSection('request')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">请求信息</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('request') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
                                {JSON.stringify(node.request, null, 2)}
                            </pre>
                        </CollapsibleContent>
                    </Collapsible>
                )}

                {/* 响应信息 */}
                {node.response && (
                    <Collapsible
                        open={expandedSections.has('response')}
                        onOpenChange={() => toggleSection('response')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">响应信息</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('response') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
                                {JSON.stringify(node.response, null, 2)}
                            </pre>
                        </CollapsibleContent>
                    </Collapsible>
                )}

                {/* SQL 信息 */}
                {node.sql && (
                    <Collapsible
                        open={expandedSections.has('sql')}
                        onOpenChange={() => toggleSection('sql')}
                    >
                        <CollapsibleTrigger asChild>
                            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                                <span className="font-medium">SQL 语句</span>
                                <ChevronDown className={`h-4 w-4 transition-transform ${
                                    expandedSections.has('sql') ? 'rotate-180' : ''
                                }`} />
                            </Button>
                        </CollapsibleTrigger>
                        <CollapsibleContent className="mt-2">
                            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto font-mono">
                                {node.sql}
                            </pre>
                        </CollapsibleContent>
                    </Collapsible>
                )}
            </CardContent>
        </Card>
    );
};
