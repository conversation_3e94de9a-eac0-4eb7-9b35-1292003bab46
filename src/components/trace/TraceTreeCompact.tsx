import React, { useState, ReactElement } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    ChevronDown, 
    ChevronRight, 
    Eye, 
    EyeOff, 
    Hash,
    Globe,
    Database,
    Server,
    MessageSquare,
    Bot,
    Search,
    Layers,
    Zap,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle
} from 'lucide-react';
import './trace-tree.css';

// 类型定义
export type ServiceType = 'http' | 'thrift' | 'db' | 'redis' | 'mq' | 'agent' | 'search' | 'vector';
export type NodeStatus = 'success' | 'error' | 'timeout' | 'pending';

export interface TraceNode {
    id: string;
    type: ServiceType;
    service?: string;
    method?: string;
    duration: number;
    status: NodeStatus;
    timestamp: Date;
    tags?: string[];
    children?: TraceNode[];
    request?: any;
    response?: any;
    sql?: string;
}

interface TraceTreeCompactProps {
    traceData?: TraceNode;
}

export const TraceTreeCompact: React.FC<TraceTreeCompactProps> = ({ traceData }) => {
    const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
    const [selectedNode, setSelectedNode] = useState<TraceNode | null>(null);

    // 切换节点展开状态
    const toggleExpanded = (nodeId: string) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(nodeId)) {
            newExpanded.delete(nodeId);
        } else {
            newExpanded.add(nodeId);
        }
        setExpandedNodes(newExpanded);
    };

    // 获取服务图标
    const getServiceIcon = (type: ServiceType): ReactElement => {
        switch (type) {
            case 'http': return <Globe className="h-3 w-3"/>;
            case 'thrift': return <Zap className="h-3 w-3"/>;
            case 'db': return <Database className="h-3 w-3"/>;
            case 'redis': return <Server className="h-3 w-3"/>;
            case 'mq': return <MessageSquare className="h-3 w-3"/>;
            case 'agent': return <Bot className="h-3 w-3"/>;
            case 'search': return <Search className="h-3 w-3"/>;
            case 'vector': return <Layers className="h-3 w-3"/>;
            default: return <Server className="h-3 w-3"/>;
        }
    };

    // 获取状态图标
    const getStatusIcon = (status: NodeStatus): ReactElement => {
        switch (status) {
            case 'success': return <CheckCircle className="h-3 w-3 text-green-500"/>;
            case 'error': return <XCircle className="h-3 w-3 text-red-500"/>;
            case 'timeout': return <AlertCircle className="h-3 w-3 text-yellow-500"/>;
            case 'pending': return <Clock className="h-3 w-3 text-blue-500"/>;
            default: return <CheckCircle className="h-3 w-3 text-gray-500"/>;
        }
    };

    // 获取节点类型颜色
    const getNodeTypeColor = (type: ServiceType): string => {
        switch (type) {
            case 'http': return 'bg-blue-50 border-blue-200 text-blue-800';
            case 'thrift': return 'bg-orange-50 border-orange-200 text-orange-800';
            case 'db': return 'bg-green-50 border-green-200 text-green-800';
            case 'redis': return 'bg-red-50 border-red-200 text-red-800';
            case 'mq': return 'bg-purple-50 border-purple-200 text-purple-800';
            case 'agent': return 'bg-indigo-50 border-indigo-200 text-indigo-800';
            case 'search': return 'bg-teal-50 border-teal-200 text-teal-800';
            case 'vector': return 'bg-pink-50 border-pink-200 text-pink-800';
            default: return 'bg-gray-50 border-gray-200 text-gray-800';
        }
    };

    // 格式化持续时间
    const formatDuration = (duration: number): string => {
        if (duration < 1000) return `${duration}ms`;
        return `${(duration / 1000).toFixed(2)}s`;
    };

    // 渲染缩进连线 - 使用验证过的精确对齐逻辑
    const renderIndentLines = (level: number, isLast: boolean, ancestorLines: boolean[] = []): ReactElement[] => {
        if (level === 0) return [];

        const indentElements: ReactElement[] = [];
        const INDENT_WIDTH = 20;
        const LINE_OFFSET = 9.5;

        for (let i = 0; i < level; i++) {
            const isCurrentLevel = i === level - 1;

            if (isCurrentLevel) {
                // 当前层级 - 显示弧形连接线
                indentElements.push(
                    <span
                        key={`current-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {/* 垂直线 - 如果不是最后一个兄弟节点 */}
                        {!isLast && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}

                        {/* 弧形连接线 */}
                        <span
                            className="trace-tree-connector"
                            style={{
                                position: 'absolute',
                                left: `${LINE_OFFSET}px`,
                                top: '0',
                                width: '10px',
                                height: 'calc(50% + 6px)'
                            }}
                        ></span>
                    </span>
                );
            } else {
                // 祖先层级 - 显示垂直连接线（如果需要）
                const shouldShowLine = ancestorLines[i];
                indentElements.push(
                    <span
                        key={`ancestor-${i}`}
                        className="inline-block relative trace-tree-deep-level"
                        style={{
                            width: `${INDENT_WIDTH}px`,
                            height: '28px'
                        }}
                    >
                        {shouldShowLine && (
                            <span
                                className="trace-tree-line"
                                style={{
                                    position: 'absolute',
                                    left: `${LINE_OFFSET}px`,
                                    top: '0',
                                    bottom: '0',
                                    width: '1px'
                                }}
                            ></span>
                        )}
                    </span>
                );
            }
        }

        return indentElements;
    };

    // 渲染单个节点 - 简化版本
    const renderNode = (node: TraceNode, level: number, isLast: boolean, ancestorLines: boolean[]): ReactElement => {
        const isExpanded = expandedNodes.has(node.id);
        const hasChildren = node.children && node.children.length > 0;

        // 计算当前层级的祖先连线状态
        const currentAncestorLines = [...ancestorLines];
        if (level > 0) {
            currentAncestorLines[level - 1] = !isLast;
        }

        return (
            <div key={node.id} className="relative">
                <div
                    className={`flex items-center min-h-[28px] hover:bg-muted/50 cursor-pointer rounded-sm px-1 ${
                        selectedNode?.id === node.id ? 'bg-muted' : ''
                    }`}
                    onClick={() => setSelectedNode(node)}
                >
                    {/* 渲染缩进连线 */}
                    {level > 0 && renderIndentLines(level, isLast, ancestorLines)}

                    {/* 节点内容 */}
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                        {/* 展开/折叠按钮 */}
                        {hasChildren && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0 flex-shrink-0"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleExpanded(node.id);
                                }}
                            >
                                {isExpanded ? <ChevronDown className="h-3 w-3"/> : <ChevronRight className="h-3 w-3"/>}
                            </Button>
                        )}

                        {/* 服务图标 */}
                        <div className={`p-1 rounded border flex-shrink-0 ${getNodeTypeColor(node.type)}`}>
                            {getServiceIcon(node.type)}
                        </div>

                        {/* 服务名称 */}
                        <span className="text-sm font-medium truncate flex-1">
                            {node.service || node.method || node.id}
                        </span>

                        {/* 耗时 */}
                        <span className="text-xs text-muted-foreground flex-shrink-0">
                            {formatDuration(node.duration)}
                        </span>

                        {/* 状态图标 */}
                        <div className="flex-shrink-0">
                            {getStatusIcon(node.status)}
                        </div>
                    </div>
                </div>

                {/* 渲染子节点 */}
                {hasChildren && isExpanded && (
                    <div className="relative">
                        {node.children!.map((child, index) =>
                            renderNode(
                                child,
                                level + 1,
                                index === node.children!.length - 1,
                                currentAncestorLines
                            )
                        )}
                    </div>
                )}
            </div>
        );
    };

    // 示例数据
    const sampleData: TraceNode = traceData || {
        id: 'root',
        type: 'agent',
        service: "O'Keefe Inc-service",
        duration: 1560,
        status: 'success',
        timestamp: new Date(),
        children: [
            {
                id: 'redis-1',
                type: 'redis',
                service: 'redis-cluster',
                duration: 1930,
                status: 'error',
                timestamp: new Date(),
                children: [
                    {
                        id: 'mysql-1',
                        type: 'db',
                        service: 'mysql-cluster-67',
                        duration: 1440,
                        status: 'timeout',
                        timestamp: new Date(),
                        children: [
                            {
                                id: 'redis-2',
                                type: 'redis',
                                service: 'redis-cluster',
                                duration: 1290,
                                status: 'success',
                                timestamp: new Date(),
                                children: [
                                    {
                                        id: 'thrift-1',
                                        type: 'thrift',
                                        service: 'Schoppe and Sons-service',
                                        duration: 1440,
                                        status: 'success',
                                        timestamp: new Date(),
                                        children: []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    };

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Hash className="h-4 w-4"/>
                        链路追踪详情
                    </div>
                    <div className="flex gap-1">
                        <Button
                            variant="outline"
                            size="sm"
                            className="h-6 text-xs px-2"
                            onClick={() => setExpandedNodes(new Set())}
                        >
                            <EyeOff className="h-3 w-3 mr-1"/>
                            折叠
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            className="h-6 text-xs px-2"
                            onClick={() => {
                                const allIds = new Set<string>();
                                const collectIds = (node: TraceNode) => {
                                    allIds.add(node.id);
                                    if (node.children) {
                                        node.children.forEach(collectIds);
                                    }
                                };
                                collectIds(sampleData);
                                setExpandedNodes(allIds);
                            }}
                        >
                            <Eye className="h-3 w-3 mr-1"/>
                            展开
                        </Button>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent className="p-3">
                <div className="trace-tree-container">
                    {renderNode(sampleData, 0, true, [])}
                </div>
            </CardContent>
        </Card>
    );
};
