import React from "react";
import {Activity, HomeIcon, Settings as SettingsIcon} from "lucide-react";
import Index from "./pages/Index.tsx";
import Settings from "./pages/Settings.tsx";
import Unauthorized from "./pages/Unauthorized.tsx";
import ServerError from "./pages/ServerError.tsx";
import TraceTest from "./pages/TraceTest.tsx";

export interface NavItem {
  title: string;
  to: string;
  icon: React.ReactElement;
  page: React.ReactElement;
  hidden?: boolean;
}

/**
 * Central place for defining the navigation items. Used for navigation components and routing.
 */
export const navItems: NavItem[] = [
  {
    title: "Home",
    to: "/",
    icon: <HomeIcon className="h-4 w-4" />,
    page: <Index />,
  },
  {
    title: "Settings",
    to: "/settings",
    icon: <SettingsIcon className="h-4 w-4" />,
    page: <Settings />,
  },
  {
    title: "Trace Test",
    to: "/trace-test",
    icon: <Activity className="h-4 w-4" />,
    page: <TraceTest />,
  },
  // 无权限页面 - 生产环境需要
  {
    title: "Unauthorized",
    to: "/unauthorized",
    icon: <SettingsIcon className="h-4 w-4" />,
    page: <Unauthorized />,
    // 在导航中隐藏，但路由保持可用
    hidden: true,
  },
  // 服务器错误页面 - 仅开发环境用于测试
  ...((typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') ? [
    {
      title: "Server Error",
      to: "/server-error",
      icon: <SettingsIcon className="h-4 w-4" />,
      page: <ServerError />,
    },
  ] : []),
];

