// 会话管理服务

import {BaseAPIService} from '../api/base';
import {Conversation} from '../types';
import {isMockEnabled, mockApiService, mockConversationService} from '@/mock';

export class ConversationService extends BaseAPIService {
    // 获取会话列表
    async getConversations(): Promise<Conversation[]> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockConversationService.getConversations();
        }

        // 真实 API 调用
        const conversations = await this.request<any[]>('/conversations');
        return conversations.map((conv: any) => ({
            ...conv,
            createdAt: new Date(conv.createdAt),
            updatedAt: new Date(conv.updatedAt),
            messages: conv.messages.map((msg: any) => ({
                ...msg,
                timestamp: new Date(msg.timestamp)
            }))
        }));
    }

    // 保存会话
    async saveConversation(conversation: Conversation): Promise<void> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockApiService.saveConversation(conversation);
        }

        // 真实 API 调用
        await this.request(`/conversations/${conversation.id}`, {
            method: 'PUT',
            body: JSON.stringify(conversation)
        });
    }

    // 删除会话
    async deleteConversation(conversationId: string): Promise<void> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockConversationService.deleteConversation(conversationId);
        }

        // 真实 API 调用
        await this.request(`/conversations/${conversationId}`, {
            method: 'DELETE'
        });
    }
}

export const conversationService = new ConversationService();

