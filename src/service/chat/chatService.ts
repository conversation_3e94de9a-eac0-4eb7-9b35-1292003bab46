// 聊天服务

import {BaseAPIService} from '../api/base';
import {AgentResponse, CommandBlock} from '../types';
import {StreamConfig, streamService} from '@/service';
import {isMockEnabled, mockApiService, mockChatService} from '@/mock';

export class ChatService extends BaseAPIService {
    // 发送消息并获取流式响应
    async sendMessage(
        conversationId: string,
        content: string,
        commandBlocks: CommandBlock[] = [],
        config?: StreamConfig
    ): Promise<ReadableStream<AgentResponse>> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockApiService.sendMessage(conversationId, content, commandBlocks);
        }

        // 真实 API 调用
        const stream = await this.requestStream('/chat/stream', {
            method: 'POST',
            body: JSON.stringify({
                conversationId,
                content,
                commandBlocks
            })
        });

        // 使用流服务处理响应
        const {process} = streamService.createStreamProcessor(config);
        return process(stream);
    }

    // 停止生成
    async stopGeneration(conversationId: string, messageId: string): Promise<void> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockChatService.stopGeneration(conversationId, messageId);
        }

        // 真实 API 调用
        await this.request('/chat/stop', {
            method: 'POST',
            body: JSON.stringify({
                conversationId,
                messageId
            })
        });
    }

    // 重新生成响应
    async regenerateResponse(conversationId: string, messageId: string): Promise<ReadableStream<AgentResponse>> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockChatService.regenerateResponse(conversationId, messageId);
        }

        // 真实 API 调用
        const stream = await this.requestStream('/chat/regenerate', {
            method: 'POST',
            body: JSON.stringify({
                conversationId,
                messageId
            })
        });

        // 使用流服务处理响应
        const {process} = streamService.createStreamProcessor();
        return process(stream);
    }
}

export const chatService = new ChatService();

