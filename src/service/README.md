# Service 模块架构

本目录按照功能模块重新组织了服务层代码，提供更清晰的代码结构和更好的可维护性。

## 目录结构

```
src/service/
├── types/                  # 类型定义模块
│   ├── chat.ts            # 聊天相关类型
│   ├── agent.ts           # 代理相关类型
│   ├── trace.ts           # 追踪相关类型
│   ├── config.ts          # 用户配置相关类型
│   ├── share.ts           # 分享相关类型
│   └── index.ts           # 类型统一导出
├── api/                   # API 基础服务
│   └── base.ts            # 基础 API 服务类
├── chat/                  # 聊天服务模块
│   └── chatService.ts     # 聊天相关 API
├── conversation/          # 会话管理模块
│   └── conversationService.ts # 会话 CRUD 操作
├── user/                  # 用户服务模块
│   └── userService.ts     # 用户配置管理
├── share/                 # 分享服务模块
│   └── shareService.ts    # 分享链接管理
├── fsd/                   # FSD 服务模块
│   └── fsdService.ts      # FSD 链接管理
├── stream/                # 流式处理模块
│   └── streamService.ts   # 流式响应处理
├── examples/              # 使用示例
│   └── streamUsage.ts     # 流式服务使用示例
├── index.ts               # 服务统一导出
└── README.md              # 本文件
```

## 模块说明

### 1. 类型定义模块 (`types/`)

按功能领域拆分类型定义：

- **chat.ts**: 聊天相关类型（消息、对话、命令块）
- **agent.ts**: 代理相关类型（响应类型、工具状态）
- **trace.ts**: 追踪相关类型（追踪节点、状态）
- **config.ts**: 用户配置类型（主题、样式、UnionId）
- **share.ts**: 分享相关类型（分享配置）

### 2. API 基础服务 (`api/`)

- **base.ts**: 提供基础的 HTTP 请求封装，包括普通请求和流式请求

### 3. 功能服务模块

每个功能模块都继承自 `BaseAPIService`，提供特定领域的 API 操作：

- **chatService**: 聊天消息发送、停止生成、重新生成
- **conversationService**: 会话的增删改查
- **userService**: 用户配置的获取和保存
- **shareService**: 分享链接的创建
- **fsdService**: FSD 链接的查询
- **streamService**: 流式响应的处理和状态管理

### 4. 流式处理模块 (`stream/`)

提供强大的流式响应处理能力：

- 支持重试机制
- 支持超时控制
- 支持流取消
- 支持 Agent 状态管理
- 支持多流并发管理

## 使用方式

### 1. 导入单个服务

```typescript
import { chatService, conversationService } from '@/service';

// 发送消息
const stream = await chatService.sendMessage('conv_123', 'Hello');

// 获取会话列表
const conversations = await conversationService.getConversations();
```

### 2. 使用兼容的 apiService

```typescript
import { apiService } from '@/service';

// 保持原有的使用方式
const stream = await apiService.sendMessage('conv_123', 'Hello');
const conversations = await apiService.getConversations();
```

### 3. 使用类型定义

```typescript
import { Message, Conversation, AgentResponse } from '@/service';

const message: Message = {
  id: '1',
  role: 'user',
  content: 'Hello',
  timestamp: new Date()
};
```

### 4. 流式处理

```typescript
import { streamService, StreamConfig } from '@/service';

const config: StreamConfig = {
  maxRetries: 3,
  timeout: 30000,
  onEvent: (event) => {
    console.log('Stream event:', event.type);
  }
};

// 更新 Agent 状态
streamService.updateAgentState('conv_123', {
  status: 'thinking',
  currentTool: 'analyzer'
});
```

## 迁移指南

### 从旧版本迁移

1. **类型导入**: 所有类型定义保持不变，只需更新导入路径
2. **服务使用**: `apiService` 接口保持完全兼容
3. **新功能**: 可以选择使用新的模块化服务获得更好的类型支持

### 推荐的最佳实践

1. **新代码**: 使用具体的服务模块（如 `chatService`）而不是 `apiService`
2. **类型安全**: 充分利用 TypeScript 类型检查
3. **错误处理**: 使用各服务模块提供的具体错误类型
4. **流式处理**: 使用 `streamService` 的高级功能进行状态管理

## 扩展指南

### 添加新的服务模块

1. 在对应目录下创建服务类
2. 继承 `BaseAPIService`
3. 在 `index.ts` 中导出
4. 添加到 `apiService` 兼容对象（如需要）

### 添加新的类型定义

1. 在 `types/` 目录下创建或修改相应文件
2. 在 `types/index.ts` 中导出
3. 更新相关服务模块的类型引用

这种模块化的架构提供了更好的代码组织、类型安全和可维护性，同时保持了向后兼容性。

