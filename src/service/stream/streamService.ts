import {AgentResponse} from '../types';

// 流式响应事件类型
export interface StreamEvent {
    type: 'start' | 'data' | 'error' | 'end' | 'retry';
    data?: AgentResponse;
    error?: Error;
    retryCount?: number;
}

// 流式响应配置
export interface StreamConfig {
    maxRetries?: number;
    retryDelay?: number;
    timeout?: number;
    onEvent?: (event: StreamEvent) => void;
}

// Agent 状态管理
export interface AgentState {
    conversationId: string;
    messageId?: string;
    status: 'idle' | 'thinking' | 'processing' | 'streaming' | 'completed' | 'error';
    currentTool?: string;
    progress?: number;
    metadata?: Record<string, any>;
}

// Agent 命令类型
export interface AgentCommand {
    type: 'start' | 'stop' | 'retry' | 'update_state';
    payload?: any;
}

export class StreamService {
    private activeStreams = new Map<string, AbortController>();
    private agentStates = new Map<string, AgentState>();

    // 创建流式响应处理器
    createStreamProcessor(config: StreamConfig = {}): {
        process: (stream: ReadableStream<Uint8Array>) => ReadableStream<AgentResponse>;
        controller: AbortController;
    } {
        const controller = new AbortController();
        const { maxRetries = 3, retryDelay = 1000, timeout = 30000, onEvent } = config;

        const process = (stream: ReadableStream<Uint8Array>): ReadableStream<AgentResponse> => {
            return new ReadableStream<AgentResponse>({
                start(streamController) {
                    let retryCount = 0;
                    let timeoutId: NodeJS.Timeout;

                    const processStream = async () => {
                        try {
                            // 设置超时
                            if (timeout > 0) {
                                timeoutId = setTimeout(() => {
                                    controller.abort();
                                    streamController.error(new Error('流式响应超时'));
                                }, timeout);
                            }

                            // 触发开始事件
                            onEvent?.({ type: 'start' });

                            const reader = stream.getReader();
                            const decoder = new TextDecoder();
                            let buffer = '';

                            while (true) {
                                if (controller.signal.aborted) {
                                    break;
                                }

                                const { done, value } = await reader.read();

                                if (done) {
                                    // 处理剩余的缓冲区内容
                                    if (buffer.trim()) {
                                        try {
                                            const response = JSON.parse(buffer) as AgentResponse;
                                            onEvent?.({ type: 'data', data: response });
                                            streamController.enqueue(response);
                                        } catch (e) {
                                            console.warn('解析最后的响应数据失败:', e);
                                        }
                                    }
                                    break;
                                }

                                // 解码数据
                                const chunk = decoder.decode(value, { stream: true });
                                buffer += chunk;

                                // 按行分割处理
                                const lines = buffer.split('\n');
                                buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

                                for (const line of lines) {
                                    const trimmedLine = line.trim();
                                    if (!trimmedLine) continue;

                                    try {
                                        // 处理 SSE 格式
                                        if (trimmedLine.startsWith('data: ')) {
                                            const jsonStr = trimmedLine.slice(6);
                                            if (jsonStr === '[DONE]') {
                                                onEvent?.({ type: 'end' });
                                                streamController.close();
                                                return;
                                            }

                                            const response = JSON.parse(jsonStr) as AgentResponse;
                                            onEvent?.({ type: 'data', data: response });
                                            streamController.enqueue(response);
                                        } else {
                                            // 直接 JSON 格式
                                            const response = JSON.parse(trimmedLine) as AgentResponse;
                                            onEvent?.({ type: 'data', data: response });
                                            streamController.enqueue(response);
                                        }
                                    } catch (e) {
                                        console.warn('解析响应数据失败:', e, '原始数据:', trimmedLine);
                                    }
                                }
                            }

                            onEvent?.({ type: 'end' });
                            streamController.close();

                        } catch (error) {
                            console.error('流式处理错误:', error);

                            // 重试逻辑
                            if (retryCount < maxRetries && !controller.signal.aborted) {
                                retryCount++;
                                onEvent?.({ type: 'retry', retryCount });

                                setTimeout(() => {
                                    processStream();
                                }, retryDelay);
                            } else {
                                onEvent?.({ type: 'error', error: error as Error });
                                streamController.error(error);
                            }
                        } finally {
                            if (timeoutId) {
                                clearTimeout(timeoutId);
                            }
                        }
                    };

                    processStream();
                },

                cancel() {
                    controller.abort();
                }
            });
        };

        return { process, controller };
    }

    // 注册活动流
    registerStream(streamId: string, controller: AbortController): void {
        this.activeStreams.set(streamId, controller);
    }

    // 停止流
    stopStream(streamId: string): void {
        const controller = this.activeStreams.get(streamId);
        if (controller) {
            controller.abort();
            this.activeStreams.delete(streamId);
        }
    }

    // 停止所有流
    stopAllStreams(): void {
        for (const [, controller] of this.activeStreams) {
            controller.abort();
        }
        this.activeStreams.clear();
    }

    // 更新 Agent 状态
    updateAgentState(conversationId: string, state: Partial<AgentState>): void {
        const currentState = this.agentStates.get(conversationId) || {
            conversationId,
            status: 'idle'
        };

        const newState = { ...currentState, ...state };
        this.agentStates.set(conversationId, newState);
    }

    // 获取 Agent 状态
    getAgentState(conversationId: string): AgentState | undefined {
        return this.agentStates.get(conversationId);
    }

    // 清理 Agent 状态
    clearAgentState(conversationId: string): void {
        this.agentStates.delete(conversationId);
    }

    // 发送 Agent 命令
    sendAgentCommand(conversationId: string, command: AgentCommand): void {
        // 这里可以实现命令处理逻辑
        console.log(`发送 Agent 命令 [${conversationId}]:`, command);

        switch (command.type) {
            case 'stop':
                this.stopStream(conversationId);
                this.updateAgentState(conversationId, { status: 'idle' });
                break;
            case 'update_state':
                this.updateAgentState(conversationId, command.payload);
                break;
        }
    }

    // 获取活动流数量
    getActiveStreamCount(): number {
        return this.activeStreams.size;
    }

    // 获取所有活动流 ID
    getActiveStreamIds(): string[] {
        return Array.from(this.activeStreams.keys());
    }
}

export const streamService = new StreamService();

