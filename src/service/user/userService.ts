// 用户配置服务

import {BaseAPIService} from '../api/base';
import {UserConfig} from '../types';
import {isMockEnabled, mockApiService, mockUserService} from '@/mock';

export class UserService extends BaseAPIService {
    // 获取用户配置
    async getUserConfig(): Promise<UserConfig> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockApiService.getUserConfig();
        }

        // 真实 API 调用
        return this.request<UserConfig>('/user/config');
    }

    // 保存用户配置
    async saveUserConfig(config: UserConfig): Promise<void> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockUserService.saveUserConfig(config);
        }

        // 真实 API 调用
        await this.request('/user/config', {
            method: 'PUT',
            body: JSON.stringify(config)
        });
    }
}

export const userService = new UserService();

