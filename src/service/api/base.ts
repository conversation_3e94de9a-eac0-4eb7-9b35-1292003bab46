// 基础 API 服务

export class BaseAPIService {
    protected baseURL = '/api';

    protected async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
            ...options,
        });

        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.status}`);
        }

        return response.json();
    }

    protected async requestStream(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<ReadableStream<Uint8Array>> {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            },
            ...options,
        });

        if (!response.ok) {
            throw new Error(`API 请求失败: ${response.status}`);
        }

        if (!response.body) {
            throw new Error('响应体为空');
        }

        return response.body;
    }
}

