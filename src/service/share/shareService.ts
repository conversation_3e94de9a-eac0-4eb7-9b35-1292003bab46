// 分享服务

import {BaseAPIService} from '../api/base';
import {ShareConfig} from '../types';
import {isMockEnabled, mockShareService} from '@/mock';

export class ShareService extends BaseAPIService {
    // 创建分享链接
    async createShare(
        conversationId: string,
        type: 'dynamic' | 'static',
        expiresAt?: Date
    ): Promise<ShareConfig> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockShareService.createShare(conversationId, type, expiresAt);
        }

        // 真实 API 调用
        return this.request<ShareConfig>('/shares', {
            method: 'POST',
            body: JSON.stringify({
                conversationId,
                type,
                expiresAt
            })
        });
    }
}

export const shareService = new ShareService();

