// 服务模块统一导出

// 类型定义
export * from './types';

// 服务实例
export {chatService} from './chat/chatService';
export {conversationService} from './conversation/conversationService';
export {userService} from './user/userService';
export {shareService} from './share/shareService';
export {fsdService} from './fsd/fsdService';
export {streamService} from './stream/streamService';

// 流相关类型和配置
export type {StreamConfig, StreamEvent, AgentState, AgentCommand} from './stream/streamService';

// 兼容性：保持原有的 apiService 接口
import {chatService} from './chat/chatService';
import {conversationService} from './conversation/conversationService';
import {userService} from './user/userService';
import {shareService} from './share/shareService';
import {fsdService} from './fsd/fsdService';

// 创建兼容的 apiService 对象
export const apiService = {
    // 聊天相关
    sendMessage: chatService.sendMessage.bind(chatService),
    stopGeneration: chatService.stopGeneration.bind(chatService),
    regenerateResponse: chatService.regenerateResponse.bind(chatService),

    // 会话相关
    getConversations: conversationService.getConversations.bind(conversationService),
    saveConversation: conversationService.saveConversation.bind(conversationService),
    deleteConversation: conversationService.deleteConversation.bind(conversationService),

    // 用户配置相关
    getUserConfig: userService.getUserConfig.bind(userService),
    saveUserConfig: userService.saveUserConfig.bind(userService),

    // 分享相关
    createShare: shareService.createShare.bind(shareService),

    // FSD 相关
    getFSDLinks: fsdService.getFSDLinks.bind(fsdService),
};
