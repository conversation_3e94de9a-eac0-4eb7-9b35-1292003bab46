// FSD 链接服务

import {BaseAPIService} from '../api/base';
import {FSDLink} from '../types';
import {isMockEnabled, mockFsdService} from '@/mock';

export class FSDService extends BaseAPIService {
    // 获取 FSD 链接列表
    async getFSDLinks(query?: string): Promise<FSDLink[]> {
        // 如果启用 mock，使用 mock 服务
        if (isMockEnabled()) {
            return mockFsdService.getFSDLinks(query);
        }

        // 真实 API 调用
        const endpoint = query
            ? `/fsd/links?query=${encodeURIComponent(query)}`
            : '/fsd/links';

        return this.request<FSDLink[]>(endpoint);
    }
}

export const fsdService = new FSDService();

