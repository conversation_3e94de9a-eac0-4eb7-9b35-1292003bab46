// 用户配置相关类型定义

export enum Theme {
    Light = 'light',
    Dark = 'dark',
    System = 'system'
}

export enum ChatStyle {
    Bubble = 'bubble',
    Flat = 'flat'
}

export interface UnionIdItem {
    id: string;
    value: string;
    name: string;
    description: string;
    createdAt: Date;
    updatedAt: Date;
    tags: string[];
    isActive: boolean;
}

export interface UserConfig {
    unionId?: string;
    theme: Theme;
    chatStyle?: ChatStyle;
    unionIds?: UnionIdItem[];
}

export interface FSDLink {
    id: string;
    name: string;
    url: string;
    description?: string;
}

