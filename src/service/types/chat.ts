// 聊天相关类型定义

export enum CommandBlockType {
    TraceId = 'TraceId',
    UnionId = 'UnionId',
    FSD = 'FSD'
}

export enum MessageRole {
    User = 'user',
    Assistant = 'assistant'
}

export enum MessageStatus {
    Sending = 'sending',
    Streaming = 'streaming',
    Completed = 'completed',
    Error = 'error'
}

export interface CommandBlock {
    id: string;
    type: CommandBlockType;
    value: string;
    displayValue?: string;
}

export interface Message {
    id: string;
    role: MessageRole;
    content: string;
    commandBlocks?: CommandBlock[];
    timestamp: Date;
    status?: MessageStatus;
    siblings?: Message[];
    currentSiblingIndex?: number;
    toolResult?: import('./trace').TraceNode; // 引用 trace 模块的类型
}

export interface Conversation {
    id: string;
    title: string;
    messages: Message[];
    createdAt: Date;
    updatedAt: Date;
}

