// 追踪相关类型定义

export enum TraceNodeType {
    Http = 'http',
    Thrift = 'thrift',
    Db = 'db',
    Redis = 'redis',
    Mq = 'mq'
}

export enum TraceNodeStatus {
    Success = 'success',
    Error = 'error',
    Timeout = 'timeout'
}

export interface TraceNode {
    id: string;
    type: TraceNodeType;
    service?: string;
    method?: string;
    url?: string;
    sql?: string;
    key?: string;
    request?: Record<string, unknown>;
    response?: Record<string, unknown>;
    duration: number;
    status: TraceNodeStatus;
    timestamp: Date;
    children?: TraceNode[];
}

