// 代理相关类型定义

export enum AgentResponseType {
    Text = 'text',
    ToolCall = 'tool_call',
    ToolResult = 'tool_result',
    Thinking = 'thinking',
    HumanInputRequired = 'human_input_required'
}

export enum ToolStatus {
    Running = 'running',
    Completed = 'completed',
    Error = 'error'
}

export interface AgentResponse {
    type: AgentResponseType;
    content: string;
    toolName?: string;
    toolStatus?: ToolStatus;
    toolDuration?: number;
    toolParams?: Record<string, unknown>;
    toolResult?: import('./trace').TraceNode; // 引用 trace 模块的类型
    metadata?: Record<string, unknown>;
}

