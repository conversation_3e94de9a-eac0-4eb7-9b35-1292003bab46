// Mock 分享服务

import {ShareConfig} from '@/service/types';
import {generateShareConfig} from '../data/generators';
import {getMockConfig} from '../config';
import {mockDelay, shouldMockError} from '../utils';

export class MockShareService {
    private shares: ShareConfig[] = [];

    // 创建分享链接
    async createShare(
        conversationId: string,
        type: 'dynamic' | 'static',
        expiresAt?: Date
    ): Promise<ShareConfig> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('创建分享链接失败');
        }

        const shareConfig = generateShareConfig(conversationId, type, expiresAt);
        this.shares.push(shareConfig);

        console.log(`Mock: 创建分享链接 [${shareConfig.id}] for conversation [${conversationId}]`);
        return shareConfig;
    }

    // 获取分享配置
    async getShare(shareId: string): Promise<ShareConfig | null> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('获取分享配置失败');
        }

        return this.shares.find(s => s.id === shareId) || null;
    }

    // 删除分享链接
    async deleteShare(shareId: string): Promise<void> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('删除分享链接失败');
        }

        this.shares = this.shares.filter(s => s.id !== shareId);
        console.log(`Mock: 删除分享链接 [${shareId}]`);
    }

    // 获取会话的所有分享链接
    async getSharesByConversation(conversationId: string): Promise<ShareConfig[]> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('获取分享链接列表失败');
        }

        return this.shares.filter(s => s.conversationId === conversationId);
    }
}

export const mockShareService = new MockShareService();

