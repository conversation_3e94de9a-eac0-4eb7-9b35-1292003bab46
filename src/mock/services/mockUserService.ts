// Mock 用户服务

import {UserConfig} from '@/service/types';
import {generateUserConfig} from '../data/generators';
import {getMockConfig} from '../config';
import {mockDelay, shouldMockError} from '../utils';

export class MockUserService {
    private userConfig: UserConfig;

    constructor() {
        // 初始化用户配置
        this.userConfig = generateUserConfig();
    }

    // 获取用户配置
    async getUserConfig(): Promise<UserConfig> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('获取用户配置失败');
        }

        return { ...this.userConfig };
    }

    // 保存用户配置
    async saveUserConfig(config: UserConfig): Promise<void> {
        const mockConfig = getMockConfig();
        await mockDelay(mockConfig.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('保存用户配置失败');
        }

        this.userConfig = { ...config };
        console.log('Mock: 保存用户配置', config);
    }

    // 重置用户配置
    async resetUserConfig(): Promise<void> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        this.userConfig = generateUserConfig();
        console.log('Mock: 重置用户配置');
    }
}

export const mockUserService = new MockUserService();

