// Mock FSD 服务

import {FSDLink} from '@/service/types';
import {generateFSDLink} from '../data/generators';
import {getMockConfig} from '../config';
import {mockDelay, shouldMockError} from '../utils';

export class MockFsdService {
    private fsdLinks: FSDLink[] = [];

    constructor() {
        // 初始化一些示例 FSD 链接
        this.fsdLinks = Array.from({ length: 20 }, () => generateFSDLink());
    }

    // 获取 FSD 链接列表
    async getFSDLinks(query?: string): Promise<FSDLink[]> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('获取 FSD 链接失败');
        }

        let results = [...this.fsdLinks];

        // 如果有查询条件，进行过滤
        if (query) {
            const lowerQuery = query.toLowerCase();
            results = results.filter(link =>
                link.name.toLowerCase().includes(lowerQuery) ||
                link.description?.toLowerCase().includes(lowerQuery) ||
                link.url.toLowerCase().includes(lowerQuery)
            );
        }

        console.log(`Mock: 获取 FSD 链接，查询条件: ${query || '无'}，结果数量: ${results.length}`);
        return results;
    }

    // 添加 FSD 链接
    async addFSDLink(link: Omit<FSDLink, 'id'>): Promise<FSDLink> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('添加 FSD 链接失败');
        }

        const newLink: FSDLink = {
            ...link,
            id: `fsd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        this.fsdLinks.push(newLink);
        console.log(`Mock: 添加 FSD 链接 [${newLink.id}]`);
        return newLink;
    }

    // 删除 FSD 链接
    async deleteFSDLink(linkId: string): Promise<void> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('删除 FSD 链接失败');
        }

        this.fsdLinks = this.fsdLinks.filter(link => link.id !== linkId);
        console.log(`Mock: 删除 FSD 链接 [${linkId}]`);
    }

    // 更新 FSD 链接
    async updateFSDLink(linkId: string, updates: Partial<Omit<FSDLink, 'id'>>): Promise<FSDLink> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('更新 FSD 链接失败');
        }

        const linkIndex = this.fsdLinks.findIndex(link => link.id === linkId);
        if (linkIndex === -1) {
            throw new Error('FSD 链接不存在');
        }

        this.fsdLinks[linkIndex] = { ...this.fsdLinks[linkIndex], ...updates };
        console.log(`Mock: 更新 FSD 链接 [${linkId}]`);
        return this.fsdLinks[linkIndex];
    }
}

export const mockFsdService = new MockFsdService();

