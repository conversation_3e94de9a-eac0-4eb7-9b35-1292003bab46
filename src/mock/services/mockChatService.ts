// Mock 聊天服务

import {AgentResponse, CommandBlock} from '@/service/types';
import {generateResponseText} from '../data/responses';
import {getMockConfig} from '../config';
import {mockDelay, shouldMockError} from '../utils';

export class MockChatService {
    // 发送消息并获取流式响应
    async sendMessage(
        conversationId: string,
        content: string,
        commandBlocks: CommandBlock[] = []
    ): Promise<ReadableStream<AgentResponse>> {
        return new ReadableStream({
            start: (controller) => {
                this.simulateAgentResponse(controller, conversationId, content, commandBlocks);
            }
        });
    }

    private async simulateAgentResponse(
        controller: ReadableStreamDefaultController<AgentResponse>,
        conversationId: string,
        content: string,
        commandBlocks: CommandBlock[]
    ) {
        try {
            const config = getMockConfig();

            // 检查是否应该模拟错误
            if (shouldMockError()) {
                controller.enqueue({
                    type: 'text',
                    content: '抱歉，处理您的请求时出现了问题，请重试。'
                });
                controller.close();
                return;
            }

            // 思考阶段
            controller.enqueue({
                type: 'thinking',
                content: '正在分析您的请求...'
            });

            await mockDelay(config.responseDelay.thinking);

            // 工具调用阶段（如果有命令块）
            if (commandBlocks.length > 0) {
                for (const block of commandBlocks) {
                    controller.enqueue({
                        type: 'tool_call',
                        content: `正在处理 ${block.type}: ${block.value}`,
                        toolName: `${block.type}Analyzer`,
                        toolStatus: 'running'
                    });

                    await mockDelay(config.responseDelay.toolCall);

                    controller.enqueue({
                        type: 'tool_result',
                        content: `${block.type} 分析完成`,
                        toolName: `${block.type}Analyzer`,
                        toolStatus: 'completed',
                        toolDuration: config.responseDelay.toolCall
                    });
                }
            }

            // 生成响应文本
            const responseText = generateResponseText(content, commandBlocks);
            const words = responseText.split('');

            // 流式输出文本
            let currentText = '';
            for (let i = 0; i < words.length; i++) {
                currentText += words[i];

                controller.enqueue({
                    type: 'text',
                    content: currentText
                });

                // 模拟打字效果
                if (i % 5 === 0) {
                    await mockDelay(config.responseDelay.streaming);
                }
            }

            controller.close();

        } catch {
            controller.enqueue({
                type: 'text',
                content: '处理请求时发生错误，请重试。'
            });
            controller.close();
        }
    }

    // 停止生成
    async stopGeneration(conversationId: string, _messageId: string): Promise<void> {
        await mockDelay(100);
        console.log(`Mock: 停止生成 [${conversationId}]`);
    }

    // 重新生成响应
    async regenerateResponse(conversationId: string, _messageId: string): Promise<ReadableStream<AgentResponse>> {
        // 重新生成时使用相同的逻辑，但可以生成不同的内容
        return this.sendMessage(conversationId, '重新生成响应', []);
    }
}

export const mockChatService = new MockChatService();

