// Mock 会话管理服务

import {Conversation} from '@/service/types';
import {generateConversation} from '../data/generators';
import {getMockConfig} from '../config';
import {mockDelay, shouldMockError} from '../utils';

export class MockConversationService {
    private conversations: Conversation[] = [];

    constructor() {
        // 初始化一些示例会话
        this.conversations = Array.from({ length: 5 }, () => generateConversation());
    }

    // 获取会话列表
    async getConversations(): Promise<Conversation[]> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('获取会话列表失败');
        }

        return [...this.conversations];
    }

    // 保存会话
    async saveConversation(conversation: Conversation): Promise<void> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('保存会话失败');
        }

        const existingIndex = this.conversations.findIndex(c => c.id === conversation.id);
        if (existingIndex >= 0) {
            this.conversations[existingIndex] = { ...conversation, updatedAt: new Date() };
        } else {
            this.conversations.push({ ...conversation, createdAt: new Date(), updatedAt: new Date() });
        }

        console.log(`Mock: 保存会话 [${conversation.id}]`);
    }

    // 删除会话
    async deleteConversation(conversationId: string): Promise<void> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('删除会话失败');
        }

        this.conversations = this.conversations.filter(c => c.id !== conversationId);
        console.log(`Mock: 删除会话 [${conversationId}]`);
    }

    // 获取单个会话
    async getConversation(conversationId: string): Promise<Conversation | null> {
        const config = getMockConfig();
        await mockDelay(config.responseDelay.api);

        if (shouldMockError()) {
            throw new Error('获取会话失败');
        }

        return this.conversations.find(c => c.id === conversationId) || null;
    }
}

export const mockConversationService = new MockConversationService();

