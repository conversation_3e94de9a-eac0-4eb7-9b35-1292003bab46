// Mock 服务模块统一导出

// 导出各个 mock 服务
export {mockChatService} from './mockChatService';
export {mockConversationService} from './mockConversationService';
export {mockUserService} from './mockUserService';
export {mockShareService} from './mockShareService';
export {mockFsdService} from './mockFsdService';

// 兼容性：保持原有的 mockApiService 接口
import {mockChatService} from './mockChatService';
import {mockConversationService} from './mockConversationService';
import {mockFsdService} from './mockFsdService';
import {mockShareService} from './mockShareService';
import {mockUserService} from './mockUserService';

// 创建兼容的 mockApiService 对象
export const mockApiService = {
    // 聊天相关
    sendMessage: mockChatService.sendMessage.bind(mockChatService),
    stopGeneration: mockChatService.stopGeneration.bind(mockChatService),
    regenerateResponse: mockChatService.regenerateResponse.bind(mockChatService),

    // 会话相关
    getConversations: mockConversationService.getConversations.bind(mockConversationService),
    saveConversation: mockConversationService.saveConversation.bind(mockConversationService),
    deleteConversation: mockConversationService.deleteConversation.bind(mockConversationService),

    // 用户配置相关
    getUserConfig: mockUserService.getUserConfig.bind(mockUserService),
    saveUserConfig: mockUserService.saveUserConfig.bind(mockUserService),

    // 分享相关
    createShare: mockShareService.createShare.bind(mockShareService),

    // FSD 相关
    getFSDLinks: mockFsdService.getFSDLinks.bind(mockFsdService),
};

