# Mock 服务模块

本目录包含按功能模块拆分的 Mock 服务，与 `src/service` 目录的结构保持一致。

## 目录结构

```
src/mock/services/
├── mockChatService.ts          # Mock 聊天服务
├── mockConversationService.ts  # Mock 会话管理服务
├── mockUserService.ts          # Mock 用户配置服务
├── mockShareService.ts         # Mock 分享服务
├── mockFsdService.ts           # Mock FSD 服务
├── index.ts                    # 服务统一导出
└── README.md                   # 本文件
```

## 模块说明

### 1. Mock 聊天服务 (`mockChatService.ts`)

提供聊天相关的 Mock 功能：

- **sendMessage**: 模拟流式聊天响应，支持思考、工具调用、文本生成等阶段
- **stopGeneration**: 模拟停止生成操作
- **regenerateResponse**: 模拟重新生成响应

### 2. Mock 会话管理服务 (`mockConversationService.ts`)

提供会话管理的 Mock 功能：

- **getConversations**: 返回模拟的会话列表
- **saveConversation**: 模拟保存会话操作
- **deleteConversation**: 模拟删除会话操作
- **getConversation**: 获取单个会话

### 3. Mock 用户配置服务 (`mockUserService.ts`)

提供用户配置的 Mock 功能：

- **getUserConfig**: 返回模拟的用户配置
- **saveUserConfig**: 模拟保存用户配置
- **resetUserConfig**: 重置用户配置

### 4. Mock 分享服务 (`mockShareService.ts`)

提供分享功能的 Mock：

- **createShare**: 创建分享链接
- **getShare**: 获取分享配置
- **deleteShare**: 删除分享链接
- **getSharesByConversation**: 获取会话的所有分享链接

### 5. Mock FSD 服务 (`mockFsdService.ts`)

提供 FSD 链接管理的 Mock：

- **getFSDLinks**: 获取 FSD 链接列表，支持查询过滤
- **addFSDLink**: 添加新的 FSD 链接
- **deleteFSDLink**: 删除 FSD 链接
- **updateFSDLink**: 更新 FSD 链接

## 使用方式

### 1. 导入单个 Mock 服务

```typescript
import { mockChatService, mockConversationService } from '@/mock';

// 使用特定的 mock 服务
const stream = await mockChatService.sendMessage('conv_123', 'Hello');
const conversations = await mockConversationService.getConversations();
```

### 2. 使用兼容的 mockApiService

```typescript
import { mockApiService } from '@/mock';

// 保持原有的使用方式
const stream = await mockApiService.sendMessage('conv_123', 'Hello');
const conversations = await mockApiService.getConversations();
```

## 特性

### 1. 流式响应模拟

Mock 聊天服务提供真实的流式响应体验：

- 思考阶段模拟
- 工具调用过程模拟
- 逐字符的文本生成效果
- 可配置的延迟时间

### 2. 数据持久化

Mock 服务在内存中维护数据状态：

- 会话数据在会话期间保持
- 用户配置可以保存和恢复
- FSD 链接支持增删改查

### 3. 错误模拟

支持模拟各种错误场景：

- 网络请求失败
- 服务器错误
- 数据验证错误

### 4. 配置化

通过 Mock 配置系统控制行为：

- 响应延迟时间
- 错误发生概率
- 数据生成规则

## 扩展指南

### 添加新的 Mock 服务

1. 创建新的服务文件（如 `mockNewService.ts`）
2. 实现对应的 Mock 方法
3. 在 `index.ts` 中导出
4. 添加到 `mockApiService` 兼容对象（如需要）

### 自定义 Mock 行为

1. 修改对应服务文件中的方法实现
2. 使用 `getMockConfig()` 获取配置
3. 使用 `mockDelay()` 和 `shouldMockError()` 工具函数

这种模块化的 Mock 架构提供了更好的代码组织和可维护性，同时保持了与真实服务的一致性。

