// Mock 系统主入口
import {defaultMockConfig, getMockConfig, isMockEnabled, type MockConfig, saveMockConfig} from './config';
import {mockApiService} from './services';

// 重新导出所有内容
export { isMockEnabled, getMockConfig, saveMockConfig, defaultMockConfig, type MockConfig };
export { mockApiService };

// 导出模块化的 mock 服务
export * from './services';
export * from './data/generators';
export * from './data/responses';
export * from './utils';

// Mock 系统类型
export interface MockSystem {
    enabled: boolean;
    config: ReturnType<typeof getMockConfig>;
    service: typeof mockApiService;
}

// 获取 Mock 系统实例
export const getMockSystem = (): MockSystem => {
    return {
        enabled: isMockEnabled(),
        config: getMockConfig(),
        service: mockApiService
    };
};
