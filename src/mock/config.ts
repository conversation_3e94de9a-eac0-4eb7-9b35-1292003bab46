// Mock 配置
export interface MockConfig {
    enabled: boolean;
    delay: {
        min: number;
        max: number;
    };
    responseDelay: {
        api: number; // API 响应延迟
        stream: number; // 流式响应延迟
    };
    errorRate: number; // 错误率 0-1
    streamDelay: number; // 流式响应延迟（向后兼容）
}

// 默认配置
export const defaultMockConfig: MockConfig = {
    enabled: (typeof window !== 'undefined' && import.meta.env.VITE_MOCK_ENABLED === 'true') ||
        (import.meta.env.DEV && import.meta.env.VITE_MOCK_ENABLED !== 'false'),
    delay: {
        min: Number(import.meta.env.VITE_MOCK_DELAY_MIN) || 300,
        max: Number(import.meta.env.VITE_MOCK_DELAY_MAX) || 1500
    },
    responseDelay: {
        api: Number(import.meta.env.VITE_MOCK_API_DELAY) || 500,
        stream: Number(import.meta.env.VITE_MOCK_STREAM_DELAY) || 30
    },
    errorRate: Number(import.meta.env.VITE_MOCK_ERROR_RATE) || 0.01,
    streamDelay: Number(import.meta.env.VITE_MOCK_STREAM_DELAY) || 30
};

// 获取当前 mock 配置
export const getMockConfig = (): MockConfig => {
    if (typeof window === 'undefined') {
        return defaultMockConfig;
    }

    const stored = localStorage.getItem('mockConfig');
    if (stored) {
        try {
            return {...defaultMockConfig, ...JSON.parse(stored)};
        } catch {
            return defaultMockConfig;
        }
    }
    return defaultMockConfig;
};

// 保存 mock 配置
export const saveMockConfig = (config: Partial<MockConfig>): void => {
    if (typeof window === 'undefined') {
        return;
    }

    const currentConfig = getMockConfig();
    const newConfig = {...currentConfig, ...config};
    localStorage.setItem('mockConfig', JSON.stringify(newConfig));
};

// 检查是否启用 mock
export const isMockEnabled = (): boolean => {
    return getMockConfig().enabled;
};

