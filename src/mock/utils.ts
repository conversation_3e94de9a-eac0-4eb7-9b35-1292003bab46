import {getMockConfig} from './config';

// 模拟延迟
export const mockDelay = async (customDelay?: number): Promise<void> => {
    if (customDelay !== undefined) {
        return new Promise(resolve => setTimeout(resolve, customDelay));
    }

    const config = getMockConfig();
    const delay = Math.random() * (config.delay.max - config.delay.min) + config.delay.min;
    return new Promise(resolve => setTimeout(resolve, delay));
};

// 模拟错误
export const shouldMockError = (): boolean => {
    const config = getMockConfig();
    return Math.random() < config.errorRate;
};

// 生成随机 ID
export const generateId = (prefix = ''): string => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `${prefix}${timestamp}_${random}`;
};

// 生成随机时间戳
export const generateTimestamp = (daysAgo = 0): Date => {
    const now = new Date();
    const timestamp = now.getTime() - (daysAgo * 24 * 60 * 60 * 1000) + (Math.random() * 24 * 60 * 60 * 1000);
    return new Date(timestamp);
};

// 随机选择数组元素
export const randomChoice = <T>(array: T[]): T => {
    return array[Math.floor(Math.random() * array.length)];
};

// 随机布尔值
export const randomBoolean = (probability = 0.5): boolean => {
    return Math.random() < probability;
};

// 随机数字范围
export const randomNumber = (min: number, max: number): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

