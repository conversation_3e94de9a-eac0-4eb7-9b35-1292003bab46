import {faker} from '@faker-js/faker';
import {CommandBlock} from '@/service/types';
import {randomChoice, randomNumber} from '../utils';

// 响应模板
const responseTemplates = {
    traceId: [
        `我已经分析了您提供的 TraceId 对应的调用链路。从链路数据可以看出，这是一个典型的{businessType}流程，涉及{services}等多个微服务。

整个链路的执行时间为 {duration} 秒，其中{slowestService}的调用耗时最长（{slowestDuration}秒），建议关注{slowestService}的性能优化。

在数据库查询方面，{dbAnalysis}。

Redis 缓存命中率为 {cacheHitRate}%，整体表现{cachePerformance}。

**性能优化建议：**
{suggestions}`,

        `根据 TraceId 分析结果，发现以下关键信息：

**链路概览：**
- 总耗时：{duration}ms
- 服务节点：{serviceCount}个
- 成功率：{successRate}%
- 异常节点：{errorCount}个

**性能瓶颈：**
{bottlenecks}

**优化建议：**
{optimizations}`
    ],

    unionId: [
        `根据您提供的 UnionId，我查询了该用户的相关信息和行为数据。该用户是{userLevel}用户，最近30天内有{orderCount}次下单记录，平均订单金额为 ¥{avgAmount}。

用户偏好分析显示，该用户主要购买{preferences}，建议在推荐系统中增加相关品类的权重。

从技术角度来看，该用户的请求主要通过{deviceType}发起，API 调用模式正常，{anomalyStatus}。

**用户画像总结：**
- 用户等级：{userLevel}
- 活跃度：{activityLevel}（日均访问 {dailyVisits} 次）
- 消费能力：{spendingPower}
- 设备偏好：{devicePreference}
- 异常检测：{anomalyResult}`,

        `UnionId 用户分析报告：

**基础信息：**
- 用户ID：{userId}
- 注册时间：{registerTime}
- 最后活跃：{lastActive}

**行为特征：**
{behaviorAnalysis}

**风险评估：**
{riskAssessment}`
    ],

    fsd: [
        `我已经为您打开了 FSD 服务链路图：{serviceName}

通过分析该服务的依赖关系，我发现以下关键信息：

**服务架构分析：**
- 核心服务节点：{nodeCount} 个
- 依赖深度：{depthLevel} 层
- 关键路径：{criticalPath}
- 潜在风险点：{riskPoints}

**建议优化方案：**
{optimizationSuggestions}`,

        `FSD 链路分析完成：

**架构概览：**
{architectureOverview}

**依赖关系：**
{dependencies}

**性能指标：**
{performanceMetrics}

**改进建议：**
{improvements}`
    ],

    general: [
        `我理解您的需求。基于您提供的信息，我将为您进行详细的分析和处理。

**我可以帮助您：**

🔍 **链路分析** - 提供 TraceId 可以分析调用链路性能
👤 **用户分析** - 提供 UnionId 可以查看用户行为数据
🔗 **服务依赖** - 选择 FSD 链接可以分析服务架构

**使用方法：**
- 输入 "/" 可以快速添加命令
- 支持拖拽调整命令顺序
- 可以同时使用多个命令进行综合分析

请告诉我您具体需要分析什么问题，我会提供针对性的解决方案！`,

        `根据您的查询，我为您提供以下分析：

{analysisContent}

如果您需要更详细的信息或有其他问题，请随时告诉我。我可以帮您：
- 深入分析特定的技术问题
- 提供性能优化建议
- 解释复杂的系统架构
- 协助故障排查和诊断`,

        `我已经处理了您的请求。{processingResult}

**关键发现：**
{keyFindings}

**后续建议：**
{nextSteps}

如果您需要进一步的帮助或有任何疑问，请随时联系我。`
    ]
};

// 生成变量值
const generateVariables = (commandBlocks: CommandBlock[]) => {
    const businessTypes = ['电商下单', '用户注册', '支付处理', '订单查询', '库存管理'];
    const services = ['用户服务', '订单服务', '支付服务', '库存服务', '通知服务'];
    const userLevels = ['普通', 'VIP', '钻石', '黄金', '白银'];
    const deviceTypes = ['移动端', 'PC端', '小程序'];

    return {
        businessType: randomChoice(businessTypes),
        services: services.slice(0, randomNumber(3, 5)).join('、'),
        duration: (randomNumber(500, 3000) / 1000).toFixed(1),
        slowestService: randomChoice(services),
        slowestDuration: (randomNumber(800, 2000) / 1000).toFixed(1),
        dbAnalysis: randomChoice([
            '订单表的查询使用了索引，性能良好',
            '用户表查询存在全表扫描，建议优化',
            '支付表的更新操作出现了轻微的锁等待'
        ]),
        cacheHitRate: randomNumber(75, 95),
        cachePerformance: randomChoice(['良好', '优秀', '需要优化']),
        suggestions: [
            '1. 优化数据库查询语句',
            '2. 增加缓存层提升性能',
            '3. 调整服务超时配置',
            '4. 考虑增加异步处理机制'
        ].slice(0, randomNumber(2, 4)).join('\n'),

        serviceCount: randomNumber(5, 15),
        successRate: randomNumber(95, 99.9),
        errorCount: randomNumber(0, 3),
        bottlenecks: [
            '- 数据库连接池不足',
            '- 第三方接口响应慢',
            '- 缓存穿透问题'
        ].slice(0, randomNumber(1, 3)).join('\n'),
        optimizations: [
            '- 增加数据库连接池大小',
            '- 实现接口熔断机制',
            '- 优化缓存策略'
        ].slice(0, randomNumber(2, 3)).join('\n'),

        userLevel: randomChoice(userLevels),
        orderCount: randomNumber(5, 50),
        avgAmount: randomNumber(50, 500),
        preferences: randomChoice(['数码产品和图书', '服装和美妆', '食品和生活用品']),
        deviceType: randomChoice(deviceTypes),
        anomalyStatus: randomChoice(['未发现异常行为', '检测到轻微异常', '行为模式正常']),
        dailyVisits: (randomNumber(10, 50) / 10).toFixed(1),
        spendingPower: randomChoice(['高', '中等偏上', '中等', '较低']),
        devicePreference: randomChoice(['移动端为主', 'PC端为主', '多端均衡']),
        anomalyResult: randomChoice(['无异常行为', '轻微异常', '正常范围内']),

        userId: faker.string.numeric(10),
        registerTime: faker.date.past({ years: 2 }).toLocaleDateString(),
        lastActive: faker.date.recent({ days: 7 }).toLocaleDateString(),
        behaviorAnalysis: faker.lorem.paragraphs(2),
        riskAssessment: randomChoice(['低风险用户', '中等风险', '需要关注']),

        serviceName: commandBlocks.find(b => b.type === 'FSD')?.displayValue || '服务链路',
        nodeCount: randomNumber(8, 20),
        depthLevel: randomNumber(3, 6),
        criticalPath: services.slice(0, 3).join(' → '),
        riskPoints: randomChoice(['单点依赖', '循环依赖', '性能瓶颈']),
        optimizationSuggestions: [
            '1. 增加服务备用链路',
            '2. 优化服务间调用',
            '3. 加强监控告警'
        ].join('\n'),

        architectureOverview: faker.lorem.paragraph(),
        dependencies: faker.lorem.sentences(3),
        performanceMetrics: faker.lorem.sentences(2),
        improvements: faker.lorem.sentences(4),

        analysisContent: faker.lorem.paragraphs(randomNumber(1, 3)),
        processingResult: faker.lorem.sentence(),
        keyFindings: faker.lorem.sentences(randomNumber(2, 4)),
        nextSteps: faker.lorem.sentences(randomNumber(2, 3))
    };
};

// 替换模板变量
const replaceVariables = (template: string, variables: Record<string, unknown>): string => {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
        return String(variables[key] || match);
    });
};

// 生成响应文本
export const generateResponseText = (content: string, commandBlocks: CommandBlock[] = []): string => {
    const variables = generateVariables(commandBlocks);

    // 根据命令块类型选择模板
    if (commandBlocks.some(block => block.type === 'TraceId')) {
        const template = randomChoice(responseTemplates.traceId);
        return replaceVariables(template, variables);
    }

    if (commandBlocks.some(block => block.type === 'UnionId')) {
        const template = randomChoice(responseTemplates.unionId);
        return replaceVariables(template, variables);
    }

    if (commandBlocks.some(block => block.type === 'FSD')) {
        const template = randomChoice(responseTemplates.fsd);
        return replaceVariables(template, variables);
    }

    // 根据内容关键词选择响应
    if (content.toLowerCase().includes('性能') || content.toLowerCase().includes('优化')) {
        return `我理解您关注系统性能优化的需求。基于我的分析经验，我建议从以下几个维度来进行性能优化：

**1. 应用层优化**
- 代码逻辑优化，减少不必要的计算
- 缓存策略优化，提高数据访问效率
- 异步处理机制，提升并发能力

**2. 数据库优化**
- 索引优化，提升查询性能
- SQL 语句优化，减少数据库负载
- 连接池配置优化

**3. 网络层优化**
- CDN 加速，减少网络延迟
- 负载均衡配置优化
- 带宽和并发连接数优化

如果您有具体的 TraceId 或需要分析特定用户的性能问题，请提供相关信息，我可以进行更详细的分析。`;
    }

    // 默认响应
    const template = randomChoice(responseTemplates.general);
    return replaceVariables(template, variables);
};

