import {faker} from '@faker-js/faker';
import {
    <PERSON>t<PERSON>tyle,
    CommandBlock,
    CommandBlockType,
    Conversation,
    FSDLink,
    Message,
    MessageRole,
    MessageStatus,
    ShareConfig,
    ShareConfigType,
    Theme,
    TraceNode,
    TraceNodeStatus,
    TraceNodeType,
    UnionIdItem,
    UserConfig
} from '@/service';
import {generateId, generateTimestamp, randomBoolean, randomChoice, randomNumber} from '../utils';

// 设置中文语言 (faker v8+ 使用 setDefaultRefDate)
// faker.locale = 'zh_CN';

// 生成 CommandBlock
export const generateCommandBlock = (type?: CommandBlockType): CommandBlock => {
    // 使用 Object.values() 获取所有枚举值，类似 Java 的 EnumClass.values()
    const blockType = type || randomChoice(Object.values(CommandBlockType));

    const generators = {
        [CommandBlockType.TraceId]: () => ({
            id: generateId('cmd_'),
            type: CommandBlockType.TraceId,
            value: faker.string.alphanumeric(32),
            displayValue: `TraceId: ${faker.string.alphanumeric(8)}`
        }),
        [CommandBlockType.UnionId]: () => ({
            id: generateId('cmd_'),
            type: CommandBlockType.UnionId,
            value: faker.string.numeric(10),
            displayValue: `UnionId: ${faker.person.firstName()}`
        }),
        [CommandBlockType.FSD]: () => ({
            id: generateId('cmd_'),
            type: CommandBlockType.FSD,
            value: faker.internet.url(),
            displayValue: `FSD: ${faker.company.name()}服务链路`
        })
    };

    return generators[blockType]();
};

// 生成 TraceNode
export const generateTraceNode = (depth = 0, maxDepth = 5): TraceNode => {
    // 使用 Object.values() 获取枚举值列表，类似 Java 的 EnumClass.values()
    const types = Object.values(TraceNodeType);
    const type = randomChoice(types);

    const baseNode: TraceNode = {
        id: generateId('trace_'),
        type,
        duration: randomNumber(10, 2000),
        status: randomChoice(Object.values(TraceNodeStatus)),
        timestamp: generateTimestamp(randomNumber(0, 7))
    };

    // 根据类型生成特定字段
    switch (type) {
        case TraceNodeType.Http:
        case TraceNodeType.Thrift:
            Object.assign(baseNode, {
                service: faker.company.name() + '-service',
                method: randomChoice(['GET', 'POST', 'PUT', 'DELETE']),
                url: faker.internet.url(),
                request: {
                    userId: faker.string.numeric(8),
                    data: faker.lorem.words(3)
                },
                response: {
                    code: randomChoice([200, 400, 500]),
                    message: faker.lorem.sentence()
                }
            });
            break;
        case TraceNodeType.Db:
            Object.assign(baseNode, {
                service: 'mysql-cluster-' + faker.string.numeric(2),
                sql: `SELECT * FROM ${faker.lorem.word()} WHERE id = ?`,
                request: { id: faker.string.numeric(8) },
                response: { rows: randomNumber(0, 100) }
            });
            break;
        case TraceNodeType.Redis:
            Object.assign(baseNode, {
                service: 'redis-cluster',
                key: `${faker.lorem.word()}:${faker.string.numeric(8)}`,
                request: { operation: randomChoice(['GET', 'SET', 'DEL']) },
                response: { cached: randomBoolean(), ttl: randomNumber(60, 3600) }
            });
            break;
        case TraceNodeType.Mq:
            Object.assign(baseNode, {
                service: 'rabbitmq-cluster',
                request: {
                    queue: faker.lorem.word() + '.notifications',
                    message: { type: faker.lorem.word() }
                },
                response: { messageId: generateId('msg_') }
            });
            break;
    }

    // 递归生成子节点
    if (depth < maxDepth && randomBoolean(0.6)) {
        const childCount = randomNumber(1, 4);
        baseNode.children = Array.from({ length: childCount }, () =>
            generateTraceNode(depth + 1, maxDepth)
        );
    }

    return baseNode;
};

// 生成 Message
export const generateMessage = (role: MessageRole = MessageRole.User): Message => {
    const message: Message = {
        id: generateId('msg_'),
        role,
        content: role === MessageRole.User
            ? faker.lorem.sentence()
            : faker.lorem.paragraphs(randomNumber(1, 3)),
        timestamp: generateTimestamp(randomNumber(0, 30)),
        // 使用 Object.values() 获取所有枚举值
        status: randomChoice(Object.values(MessageStatus))
    };

    // 用户消息可能包含命令块
    if (role === MessageRole.User && randomBoolean(0.3)) {
        message.commandBlocks = Array.from(
            { length: randomNumber(1, 3) },
            () => generateCommandBlock()
        );
    }

    // 助手消息可能包含工具结果
    if (role === MessageRole.Assistant && randomBoolean(0.4)) {
        message.toolResult = generateTraceNode();
    }

    return message;
};

// 生成 Conversation
export const generateConversation = (): Conversation => {
    const messageCount = randomNumber(2, 20);
    const messages: Message[] = [];

    for (let i = 0; i < messageCount; i++) {
        const role = i % 2 === 0 ? MessageRole.User : MessageRole.Assistant;
        messages.push(generateMessage(role));
    }

    return {
        id: generateId('conv_'),
        title: faker.lorem.sentence(randomNumber(2, 6)),
        messages,
        createdAt: generateTimestamp(randomNumber(0, 30)),
        updatedAt: generateTimestamp(randomNumber(0, 7))
    };
};

// 生成 UnionIdItem
export const generateUnionIdItem = (): UnionIdItem => ({
    id: generateId('union_'),
    value: faker.string.numeric(10),
    name: faker.person.fullName(),
    description: faker.lorem.sentence(),
    createdAt: generateTimestamp(randomNumber(0, 365)),
    updatedAt: generateTimestamp(randomNumber(0, 30)),
    tags: Array.from({length: randomNumber(1, 5)}, () => faker.lorem.word()),
    isActive: randomBoolean(0.8)
});

// 生成 FSDLink
export const generateFSDLink = (): FSDLink => ({
    id: generateId('fsd_'),
    name: faker.company.name() + '服务链路',
    url: faker.internet.url(),
    description: faker.lorem.sentence()
});

// 生成 ShareConfig (重载版本，保持向后兼容)
export const generateShareConfig = (
    conversationId: string,
    type?: ShareConfigType,
    expiresAt?: Date
): ShareConfig => ({
    id: generateId('share_'),
    conversationId,
    // 使用 Object.values() 获取所有枚举值
    type: type || randomChoice(Object.values(ShareConfigType)),
    expiresAt: expiresAt || (randomBoolean(0.7) ? generateTimestamp(-randomNumber(1, 30)) : undefined),
    createdAt: generateTimestamp(randomNumber(0, 7))
});

// 生成 UserConfig
export const generateUserConfig = (): UserConfig => ({
    unionId: randomBoolean(0.6) ? faker.string.numeric(10) : undefined,
    // 使用 Object.values() 获取所有枚举值
    theme: randomChoice(Object.values(Theme)),
    chatStyle: randomChoice(Object.values(ChatStyle)),
    unionIds: Array.from({ length: randomNumber(0, 10) }, () => generateUnionIdItem())
});

