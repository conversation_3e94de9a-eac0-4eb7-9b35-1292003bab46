# Mock 系统使用指南

这是一个完整的 API Mock 系统，支持通过开关控制是否使用 mock 数据，提供丰富的配置选项和智能的数据生成能力。

## 🚀 快速开始

### 1. 环境配置

在项目根目录的环境变量文件中配置：

```bash
# .env.development - 开发环境（默认启用 mock）
VITE_MOCK_ENABLED=true

# .env.production - 生产环境（默认禁用 mock）
VITE_MOCK_ENABLED=false
```

### 2. 使用 Mock 控制面板

- 按 `Ctrl/Cmd + Shift + M` 打开控制面板
- 或点击右下角的 Mock 按钮
- 实时调整配置，立即生效

## 📁 系统架构

```
src/mock/
├── config.ts              # Mock 配置管理
├── utils.ts               # 通用工具函数
├── data/
│   ├── generators.ts      # 数据生成器
│   └── responses.ts       # 智能响应生成
├── services/
│   └── mockApiService.ts  # Mock API 服务
└── index.ts               # 主入口文件
```

## ⚙️ 配置选项

### MockConfig 接口

```typescript
interface MockConfig {
    enabled: boolean;           // 是否启用 mock
    delay: {                   // 响应延迟配置
        min: number;           // 最小延迟 (ms)
        max: number;           // 最大延迟 (ms)
    };
    errorRate: number;         // 错误率 (0-1)
    streamDelay: number;       // 流式响应延迟 (ms)
}
```

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_MOCK_ENABLED` | 是否启用 mock | `true` (开发环境) |
| `VITE_MOCK_DELAY_MIN` | 最小延迟 (ms) | `300` |
| `VITE_MOCK_DELAY_MAX` | 最大延迟 (ms) | `1500` |
| `VITE_MOCK_ERROR_RATE` | 错误率 | `0.05` |
| `VITE_MOCK_STREAM_DELAY` | 流式延迟 (ms) | `30` |

## 🎯 功能特性

### 1. 智能数据生成

使用 `@faker-js/faker` 生成真实感的 mock 数据：

- **用户数据**: 姓名、邮箱、电话等
- **业务数据**: 订单、商品、支付等
- **技术数据**: TraceId、链路分析、性能指标
- **时间数据**: 创建时间、更新时间等

### 2. 智能响应生成

根据用户输入和命令块类型生成相应的回复：

- **TraceId 分析**: 生成链路分析报告
- **UnionId 查询**: 生成用户画像分析
- **FSD 链路**: 生成服务架构分析
- **通用查询**: 根据关键词智能回复

### 3. 流式响应模拟

模拟真实的 AI 对话体验：

- 思考阶段提示
- 工具调用过程
- 逐字符流式输出
- 错误处理机制

### 4. 数据持久化

使用 localStorage 存储 mock 数据：

- 会话历史记录
- 用户配置信息
- 分享链接配置
- Mock 系统配置

## 🔧 API 使用

### 基础用法

```typescript
import { isMockEnabled, mockApiService } from '@/mock';

// 检查是否启用 mock
if (isMockEnabled()) {
    // 使用 mock 服务
    const response = await mockApiService.sendMessage(
        conversationId,
        content,
        commandBlocks
    );
} else {
    // 使用真实 API
    // ...
}
```

### 配置管理

```typescript
import { getMockConfig, saveMockConfig } from '@/mock';

// 获取当前配置
const config = getMockConfig();

// 更新配置
saveMockConfig({
    enabled: true,
    delay: { min: 500, max: 2000 }
});
```

### 数据生成

```typescript
import {
    generateConversation,
    generateTraceNode,
    generateUserConfig
} from '@/mock';

// 生成会话数据
const conversation = generateConversation();

// 生成链路数据
const traceData = generateTraceNode();

// 生成用户配置
const userConfig = generateUserConfig();
```

## 🎨 UI 组件

### MockControl 组件

提供可视化的 Mock 控制界面：

```tsx
import { MockControl } from '@/components/MockControl';

function App() {
    return (
        <div>
            {/* 你的应用内容 */}
            <MockControl />
        </div>
    );
}
```

### 功能特性

- 🎛️ 实时配置调整
- 📊 状态监控面板
- 🗑️ 数据清理功能
- ⌨️ 快捷键支持
- 💾 配置持久化

## 🔍 调试技巧

### 1. 控制台日志

Mock 系统会在控制台输出详细日志：

```javascript
// 查看 mock 状态
console.log('Mock enabled:', isMockEnabled());

// 查看当前配置
console.log('Mock config:', getMockConfig());
```

### 2. 数据检查

检查 localStorage 中的 mock 数据：

```javascript
// 查看会话数据
console.log(JSON.parse(localStorage.getItem('mock_conversations') || '[]'));

// 查看用户配置
console.log(JSON.parse(localStorage.getItem('mock_userConfig') || '{}'));
```

### 3. 错误模拟

调整错误率来测试错误处理：

```typescript
saveMockConfig({ errorRate: 0.5 }); // 50% 错误率
```

## 🚀 最佳实践

### 1. 开发阶段

- 启用 mock 进行快速开发
- 使用较低的延迟提高开发效率
- 适当的错误率测试异常处理

### 2. 测试阶段

- 模拟各种网络条件
- 测试不同的响应时间
- 验证错误处理逻辑

### 3. 演示阶段

- 使用稳定的 mock 数据
- 关闭错误模拟
- 优化响应时间

### 4. 生产部署

- 确保 mock 已禁用
- 移除开发环境配置
- 验证真实 API 调用

## 🔧 扩展开发

### 添加新的数据类型

1. 在 `types.ts` 中定义新类型
2. 在 `generators.ts` 中添加生成器
3. 在 `mockApiService.ts` 中实现 API
4. 在 `service/index.ts` 中添加调用逻辑

### 自定义响应模板

在 `responses.ts` 中添加新的响应模板：

```typescript
const newTemplate = `您的自定义响应模板...`;
responseTemplates.newType.push(newTemplate);
```

### 添加新的配置项

1. 更新 `MockConfig` 接口
2. 修改 `defaultMockConfig`
3. 在 `MockControl` 组件中添加 UI

## 📝 注意事项

1. **性能考虑**: 大量数据生成可能影响性能
2. **内存使用**: localStorage 有存储限制
3. **类型安全**: 确保 mock 数据类型与真实 API 一致
4. **版本兼容**: 升级时注意配置格式变化

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个 Mock 系统！

### 开发环境设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 运行类型检查
npm run type-check

