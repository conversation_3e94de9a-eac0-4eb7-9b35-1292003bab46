@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* 防止移动端橡皮筋效果 */
    overscroll-behavior: none;
    /* 优化触摸滚动 */
    -webkit-overflow-scrolling: touch;
  }
  
  /* 移动端优化 */
  @media (max-width: 640px) {
    body {
      /* 防止缩放 */
      touch-action: manipulation;
    }
  }
}

@layer components {
  /* 聊天消息样式 */
  .message-user {
    @apply bg-chat-user dark:bg-chat-user-dark;
  }
  
  .message-assistant {
    @apply bg-chat-assistant dark:bg-chat-assistant-dark;
  }

  /* 代码块样式 */
  .code-block {
    @apply bg-muted/50 border rounded-lg p-4 font-mono text-sm;
  }

  /* 链路节点样式 */
  .trace-node {
    @apply border rounded-lg p-3 transition-all duration-200 hover:shadow-md;
  }

  .trace-node-http {
    @apply border-trace-http/30 bg-trace-http/5;
  }

  .trace-node-db {
    @apply border-trace-db/30 bg-trace-db/5;
  }

  .trace-node-redis {
    @apply border-trace-redis/30 bg-trace-redis/5;
  }

  .trace-node-mq {
    @apply border-trace-mq/30 bg-trace-mq/5;
  }

  .trace-node-thrift {
    @apply border-trace-thrift/30 bg-trace-thrift/5;
  }

  /* 拖拽样式 */
  .drag-handle {
    @apply cursor-grab active:cursor-grabbing;
  }

  .drag-handle:active {
    @apply cursor-grabbing;
  }

  .dragging {
    @apply opacity-50 scale-95 rotate-2 shadow-lg;
  }

  .drag-over {
    @apply ring-2 ring-primary/50 bg-accent/50;
  }

  /* 命令块样式 */
  .command-block {
    @apply transition-all duration-200 hover:shadow-sm border-2 border-transparent;
  }

  .command-block:hover {
    @apply border-accent;
  }

  .command-block.fsd-link {
    @apply bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20;
    @apply border-blue-200 dark:border-blue-800;
  }

  .command-block.trace-id {
    @apply bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20;
    @apply border-green-200 dark:border-green-800;
  }

  .command-block.union-id {
    @apply bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20;
    @apply border-purple-200 dark:border-purple-800;
  }

  /* 命令执行结果样式 */
  .command-result {
    @apply transition-all duration-300 ease-in-out;
  }

  .command-result.executing {
    @apply animate-pulse;
  }

  .command-result.success {
    @apply border-status-success/30 bg-status-success/5;
  }

  .command-result.error {
    @apply border-status-error/30 bg-status-error/5;
  }

  /* 滚动条样式 */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--foreground));
  }

  /* 移动端触摸优化 */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* 移动端按钮优化 */
  @media (max-width: 640px) {
    .mobile-button {
      @apply min-h-[44px] px-4 text-base;
    }
    
    .mobile-icon-button {
      @apply min-h-[44px] min-w-[44px];
    }
  }

  /* 移动端侧边栏优化 */
  .mobile-sidebar {
    @apply fixed inset-0 z-50 bg-card;
    /* 添加安全区域支持 */
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 防止文本选择（在移动端） */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 触摸反馈 */
  .touch-feedback {
    @apply active:scale-95 transition-transform duration-100;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* 移动端安全区域 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* 自定义动画 */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-pulse-dot {
  animation: pulse-dot 1.5s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.3s ease-out;
}

/* 移动端特定样式 */
@media (max-width: 640px) {
  /* 优化移动端滚动性能 */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  /* 移动端输入框优化 */
  input, textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
