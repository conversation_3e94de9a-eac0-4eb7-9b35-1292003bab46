import {create} from 'zustand';
import {v4 as uuidv4} from 'uuid';
import {
    AgentResponseType,
    CommandBlock,
    Conversation,
    Message,
    MessageRole,
    MessageStatus,
    Theme,
    TraceNode,
    UserConfig
} from '@/service/types';
import {apiService} from '../service';

interface ChatState {
    // 当前会话
    currentConversation: Conversation | null;
    conversations: Conversation[];

    // UI 状态
    isLoading: boolean;
    isGenerating: boolean;
    sidebarCollapsed: boolean;

    // 用户配置
    userConfig: UserConfig;

    // Actions
    createNewConversation: () => void;
    loadConversations: () => Promise<void>;
    selectConversation: (conversationId: string) => void;
    sendMessage: (content: string, commandBlocks?: CommandBlock[]) => Promise<void>;
    stopGeneration: () => void;
    regenerateMessage: (messageId: string) => Promise<void>;
    deleteConversation: (conversationId: string) => void;
    setSidebarCollapsed: (collapsed: boolean) => void;
    updateUserConfig: (config: Partial<UserConfig>) => Promise<void>;
    switchMessageSibling: (messageId: string, direction: 'prev' | 'next') => void;
}

export const useChatStore = create<ChatState>((set, get) => ({
    currentConversation: null,
    conversations: [],
    isLoading: false,
    isGenerating: false,
    sidebarCollapsed: false,
    userConfig: {theme: Theme.System},

    createNewConversation: () => {
        // 不立即创建会话，而是清空当前会话，显示欢迎页面
        set({
            currentConversation: null
        });
    },

    loadConversations: async () => {
        set({isLoading: true});
        try {
            // 分别处理会话和用户配置的加载，避免一个失败影响另一个
            let conversations: Conversation[] = [];
            let userConfig = get().userConfig; // 使用当前配置作为默认值

            // 尝试加载会话列表
            try {
                conversations = await apiService.getConversations();
            } catch (error) {
                console.error('加载会话列表失败:', error);
                // 会话加载失败时使用空数组
                conversations = [];
            }

            // 尝试加载用户配置
            try {
                userConfig = await apiService.getUserConfig();
            } catch (error) {
                console.error('加载用户配置失败:', error);
                // 用户配置加载失败时保持当前配置不变
            }

            set({
                conversations: conversations.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()),
                userConfig,
                isLoading: false
            });

            // 如果没有当前会话且有历史会话，选择最新的
            const {currentConversation} = get();
            if (!currentConversation && conversations.length > 0) {
                set({currentConversation: conversations[0]});
            }
        } catch (error) {
            console.error('加载数据时发生未预期的错误:', error);
            set({isLoading: false});
        }
    },

    selectConversation: (conversationId: string) => {
        const {conversations} = get();
        const conversation = conversations.find(c => c.id === conversationId);
        if (conversation) {
            set({currentConversation: conversation});
        }
    },

    sendMessage: async (content: string, commandBlocks: CommandBlock[] = []) => {
        let {currentConversation} = get();

        // 如果没有当前会话，先创建一个新会话
        if (!currentConversation) {
            const newConversation: Conversation = {
                id: uuidv4(),
                title: content.slice(0, 30), // 使用消息内容作为标题
                messages: [],
                createdAt: new Date(),
                updatedAt: new Date()
            };

            set(state => ({
                currentConversation: newConversation,
                conversations: [newConversation, ...state.conversations]
            }));

            currentConversation = newConversation;
        }

        // 创建用户消息
        const userMessage: Message = {
            id: uuidv4(),
            role: MessageRole.User,
            content,
            commandBlocks,
            timestamp: new Date(),
            status: MessageStatus.Completed
        };

        // 创建助手消息占位符
        const assistantMessage: Message = {
            id: uuidv4(),
            role: MessageRole.Assistant,
            content: '',
            timestamp: new Date(),
            status: MessageStatus.Streaming
        };

        // 更新会话
        const updatedConversation = {
            ...currentConversation,
            messages: [...currentConversation.messages, userMessage, assistantMessage],
            title: currentConversation.messages.length === 0 ? content.slice(0, 30) : currentConversation.title,
            updatedAt: new Date()
        };

        set(state => ({
            currentConversation: updatedConversation,
            conversations: state.conversations.map(c =>
                c.id === updatedConversation.id ? updatedConversation : c
            ),
            isGenerating: true
        }));

        try {
            // 获取流式响应
            const stream = await apiService.sendMessage(currentConversation.id, content, commandBlocks);
            const reader = stream.getReader();

            let accumulatedContent = '';
            let toolResult: TraceNode | undefined;

            while (true) {
                const {done, value} = await reader.read();
                if (done) break;

                if (value.type === 'text') {
                    accumulatedContent = value.content;
                    // 保存 toolResult 如果存在
                    if (value.toolResult) {
                        toolResult = value.toolResult;
                    }

                    // 更新助手消息内容
                    set(state => {
                        if (!state.currentConversation) return state;

                        const updatedMessages = state.currentConversation.messages.map(msg =>
                            msg.id === assistantMessage.id
                                ? {
                                    ...msg,
                                    content: accumulatedContent,
                                    toolResult: toolResult
                                }
                                : msg
                        );

                        const updatedConv = {
                            ...state.currentConversation,
                            messages: updatedMessages
                        };

                        return {
                            currentConversation: updatedConv,
                            conversations: state.conversations.map(c =>
                                c.id === updatedConv.id ? updatedConv : c
                            )
                        };
                    });
                }
            }

            // 标记消息完成
            set(state => {
                if (!state.currentConversation) return state;

                const updatedMessages = state.currentConversation.messages.map(msg =>
                    msg.id === assistantMessage.id
                        ? {...msg, status: MessageStatus.Completed as const}
                        : msg
                );

                const finalConversation = {
                    ...state.currentConversation,
                    messages: updatedMessages
                };

                // 保存到本地存储
                apiService.saveConversation(finalConversation);

                return {
                    currentConversation: finalConversation,
                    conversations: state.conversations.map(c =>
                        c.id === finalConversation.id ? finalConversation : c
                    ),
                    isGenerating: false
                };
            });

        } catch (error) {
            console.error('发送消息失败:', error);

            // 标记消息错误
            set(state => {
                if (!state.currentConversation) return state;

                const updatedMessages = state.currentConversation.messages.map(msg =>
                    msg.id === assistantMessage.id
                        ? {...msg, status: MessageStatus.Error as const, content: '抱歉，发生了错误，请重试。'}
                        : msg
                );

                const errorConversation = {
                    ...state.currentConversation,
                    messages: updatedMessages
                };

                return {
                    currentConversation: errorConversation,
                    conversations: state.conversations.map(c =>
                        c.id === errorConversation.id ? errorConversation : c
                    ),
                    isGenerating: false
                };
            });
        }
    },

    stopGeneration: () => {
        const {currentConversation} = get();
        if (!currentConversation) return;

        const lastMessage = currentConversation.messages[currentConversation.messages.length - 1];
        if (lastMessage && lastMessage.status === MessageStatus.Streaming) {
            apiService.stopGeneration(currentConversation.id, lastMessage.id);

            set(state => {
                if (!state.currentConversation) return state;

                const updatedMessages = state.currentConversation.messages.map(msg =>
                    msg.id === lastMessage.id
                        ? {...msg, status: MessageStatus.Completed as const}
                        : msg
                );

                const updatedConversation = {
                    ...state.currentConversation,
                    messages: updatedMessages
                };

                return {
                    currentConversation: updatedConversation,
                    conversations: state.conversations.map(c =>
                        c.id === updatedConversation.id ? updatedConversation : c
                    ),
                    isGenerating: false
                };
            });
        }
    },

    regenerateMessage: async (messageId: string) => {
        const {currentConversation} = get();
        if (!currentConversation) return;

        const messageIndex = currentConversation.messages.findIndex(m => m.id === messageId);
        if (messageIndex === -1) return;

        const message = currentConversation.messages[messageIndex];

        // 创建新的 sibling 消息
        const newMessage: Message = {
            ...message,
            id: uuidv4(),
            content: '',
            status: MessageStatus.Streaming,
            timestamp: new Date(),
            toolResult: undefined
        };

        // 更新 siblings
        const updatedMessage = {
            ...message,
            siblings: message.siblings ? [...message.siblings, newMessage] : [message, newMessage],
            currentSiblingIndex: message.siblings ? message.siblings.length : 1
        };

        set(state => {
            if (!state.currentConversation) return state;

            const updatedMessages = [...state.currentConversation.messages];
            updatedMessages[messageIndex] = updatedMessage;

            const updatedConversation = {
                ...state.currentConversation,
                messages: updatedMessages
            };

            return {
                currentConversation: updatedConversation,
                conversations: state.conversations.map(c =>
                    c.id === updatedConversation.id ? updatedConversation : c
                ),
                isGenerating: true
            };
        });

        try {
            // 重新生成响应
            const stream = await apiService.regenerateResponse(currentConversation.id, messageId);
            const reader = stream.getReader();

            let accumulatedContent = '';
            let toolResult: TraceNode | undefined;

            while (true) {
                const {done, value} = await reader.read();
                if (done) break;

                if (value.type === AgentResponseType.Text) {
                    accumulatedContent = value.content;
                    if (value.toolResult) {
                        toolResult = value.toolResult;
                    }

                    // 更新新消息内容
                    set(state => {
                        if (!state.currentConversation) return state;

                        const messageIndex = state.currentConversation.messages.findIndex(m => m.id === messageId);
                        if (messageIndex === -1) return state;

                        const message = state.currentConversation.messages[messageIndex];
                        const siblings = message.siblings || [];
                        const currentIndex = message.currentSiblingIndex || 0;

                        if (siblings[currentIndex]) {
                            siblings[currentIndex] = {
                                ...siblings[currentIndex],
                                content: accumulatedContent,
                                toolResult: toolResult
                            };
                        }

                        const updatedMessages = [...state.currentConversation.messages];
                        updatedMessages[messageIndex] = {
                            ...message,
                            siblings
                        };

                        const updatedConversation = {
                            ...state.currentConversation,
                            messages: updatedMessages
                        };

                        return {
                            currentConversation: updatedConversation,
                            conversations: state.conversations.map(c =>
                                c.id === updatedConversation.id ? updatedConversation : c
                            )
                        };
                    });
                }
            }

            // 标记完成
            set(_state => ({isGenerating: false}));

        } catch (error) {
            console.error('重新生成失败:', error);
            set({isGenerating: false});
        }
    },

    deleteConversation: (conversationId: string) => {
        apiService.deleteConversation(conversationId);

        set(state => {
            const filteredConversations = state.conversations.filter(c => c.id !== conversationId);
            const newCurrentConversation = state.currentConversation?.id === conversationId
                ? (filteredConversations[0] || null)
                : state.currentConversation;

            return {
                conversations: filteredConversations,
                currentConversation: newCurrentConversation
            };
        });
    },

    setSidebarCollapsed: (collapsed: boolean) => {
        set({sidebarCollapsed: collapsed});
    },

    updateUserConfig: async (config: Partial<UserConfig>) => {
        const {userConfig} = get();
        const updatedConfig = {...userConfig, ...config};

        await apiService.saveUserConfig(updatedConfig);
        set({userConfig: updatedConfig});
    },

    switchMessageSibling: (messageId: string, direction: 'prev' | 'next') => {
        set(state => {
            if (!state.currentConversation) return state;

            const messageIndex = state.currentConversation.messages.findIndex(m => m.id === messageId);
            if (messageIndex === -1) return state;

            const message = state.currentConversation.messages[messageIndex];
            const siblings = message.siblings;
            if (!siblings || siblings.length <= 1) return state;

            const currentIndex = message.currentSiblingIndex || 0;
            let newIndex = currentIndex;

            if (direction === 'prev' && currentIndex > 0) {
                newIndex = currentIndex - 1;
            } else if (direction === 'next' && currentIndex < siblings.length - 1) {
                newIndex = currentIndex + 1;
            }

            if (newIndex === currentIndex) return state;

            const updatedMessages = [...state.currentConversation.messages];
            updatedMessages[messageIndex] = {
                ...message,
                currentSiblingIndex: newIndex
            };

            const updatedConversation = {
                ...state.currentConversation,
                messages: updatedMessages
            };

            return {
                currentConversation: updatedConversation,
                conversations: state.conversations.map(c =>
                    c.id === updatedConversation.id ? updatedConversation : c
                )
            };
        });
    }
}));
